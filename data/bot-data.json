{"commands": [{"id": "default-discord", "name": "discord", "response": "Join our Discord server at: https://discord.gg/example", "userLevel": "everyone", "cooldown": 10, "enabled": true, "usageCount": 1}, {"id": "default-uptime", "name": "uptime", "response": "Stream has been live for {uptime}", "userLevel": "everyone", "cooldown": 5, "enabled": true, "usageCount": 0}], "giveaways": {"active": null, "past": [{"id": "2qOTicaRAlaV8R7SZXQ5V", "title": "gay", "prize": "gayness", "keyword": "!join", "eligibility": "everyone", "startTime": "2025-05-29T19:44:33.893Z", "endTime": "2025-05-29T19:49:33.893Z", "timeRemaining": "4:28", "isPaused": false, "entries": [{"username": "TestUser1", "timestamp": "7:44:35 PM"}, {"username": "TestUser2", "timestamp": "7:44:35 PM"}, {"username": "TestUser3", "timestamp": "7:44:35 PM"}, {"username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timestamp": "7:44:42 PM"}], "winners": [{"username": "TestUser3", "timestamp": "7:44:45 PM"}, {"username": "TestUser1", "timestamp": "7:44:54 PM"}, {"username": "TestUser2", "timestamp": "7:44:55 PM"}, {"username": "TestUser3", "timestamp": "7:44:56 PM"}, {"username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timestamp": "7:44:58 PM"}], "announceStart": true, "startMessage": "🎉 {title} giveaway has started! Type {keyword} to enter and win {prize}! 🎉"}, {"id": "iizu8T6DLVHnI99rciymm", "title": "ballsack", "prize": "ballsack", "keyword": "!join", "eligibility": "everyone", "startTime": "2025-05-29T19:32:54.145Z", "endTime": "2025-05-29T19:37:54.145Z", "timeRemaining": "0:10", "isPaused": false, "entries": [{"username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timestamp": "7:33:51 PM"}], "winners": []}, {"id": "kxVcnuLbt3_2khvlTFRWL", "title": "ballsack", "prize": "ballsack", "keyword": "!join", "eligibility": "everyone", "startTime": "2025-05-29T19:32:54.144Z", "endTime": "2025-05-29T19:37:54.144Z", "timeRemaining": "5:00", "isPaused": false, "entries": [], "winners": []}, {"id": "yIixPYaaVvrismJnX2AZH", "title": "gw", "prize": "gw", "keyword": "gw", "eligibility": "everyone", "startTime": "2025-05-29T19:26:48.728Z", "endTime": "2025-05-29T19:31:48.728Z", "timeRemaining": "0:01", "isPaused": false, "entries": [{"username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timestamp": "7:26:51 PM"}], "winners": []}, {"id": "HBfIyBsKUpsoMaQlbUGh_", "title": "gw", "prize": "gw", "keyword": "gw", "eligibility": "everyone", "startTime": "2025-05-29T19:26:48.726Z", "endTime": "2025-05-29T19:31:48.726Z", "timeRemaining": "5:00", "isPaused": false, "entries": [], "winners": []}, {"id": "EfBYFOlMLYudFddpz_Zyy", "title": "Game Launch Party", "prize": "Game Key", "date": "2023-05-10", "entriesCount": 212, "winner": "GamerFan789"}, {"id": "Jq9jL-RNC0vgxa0u989Fo", "title": "Monthly Subscriber Giveaway", "prize": "Gaming Headset", "date": "2023-05-28", "entriesCount": 87, "winner": "SubUser456"}, {"id": "ZrtLqXXil7_Eh5p03qWC7", "title": "1000 Followers Celebration", "prize": "Steam Gift Card ($20)", "date": "2023-06-15", "entriesCount": 145, "winner": "ViewerName123"}]}, "songs": {"queue": [], "current": {"id": "HmCQEL6N7T8xt10kA7EHL", "title": "bang ajr - Official Video", "artist": "Various Artists", "thumbnail": "https://i.ytimg.com/vi/dQw4w9WgXcQ/mqdefault.jpg", "duration": 212, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "", "url": "https://www.youtube.com/results?search_query=bang%20ajr", "currentTime": 0}, "history": []}, "chat": {"messages": [{"id": "y2VyN2-ZJSQoKQ-KCAOop", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "ballsack", "color": "#48E848", "timestamp": 1748544725577, "isMod": false, "isSubscriber": false}, {"id": "nJ3pzQcCYJM8qoiwMPDTp", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "ball", "color": "#48E848", "timestamp": 1748545604864, "isMod": false, "isSubscriber": false}, {"id": "p7SJKnThxCdsdhZylQcXP", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "dog", "color": "#48E848", "timestamp": 1748546784339, "isMod": false, "isSubscriber": false}, {"id": "_kKC_N-eDeICeBCKZBc-j", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "gay", "color": "#48E848", "timestamp": 1748546790867, "isMod": false, "isSubscriber": false}, {"id": "b0tKC0rwh_KbIZMZ1EumI", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "gay", "color": "#48E848", "timestamp": 1748546792432, "isMod": false, "isSubscriber": false}, {"id": "nHFCRBSZozHj3YQ0UObjZ", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "gay", "color": "#48E848", "timestamp": 1748546792611, "isMod": false, "isSubscriber": false}, {"id": "KBR073YOYFRAxCuAffpi0", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "gay", "color": "#48E848", "timestamp": 1748546792809, "isMod": false, "isSubscriber": false}, {"id": "UL-YXED1JzkbfdHvxzntB", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "gay", "color": "#48E848", "timestamp": 1748546792954, "isMod": false, "isSubscriber": false}, {"id": "1_4TrN8W59IbLs5qp_5-1", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "gay", "color": "#48E848", "timestamp": 1748546793018, "isMod": false, "isSubscriber": false}, {"id": "EJKhC2IUW9JqgrrtTRWZE", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "gw", "color": "#48E848", "timestamp": 1748546811331, "isMod": false, "isSubscriber": false}, {"id": "F8XcZyyFm_9xdzVIg3jSK", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!discord", "color": "#48E848", "timestamp": 1748546841644, "isMod": false, "isSubscriber": false}, {"id": "WbeGudRbvGZ9ec9gUzJDE", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!!toast", "color": "#48E848", "timestamp": 1748546855669, "isMod": false, "isSubscriber": false}, {"id": "T9xWWRSHeoH4yhOdDokd7", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!join", "color": "#48E848", "timestamp": 1748547231502, "isMod": false, "isSubscriber": false}, {"id": "APaWXPYQKqbgq2GCbgK84", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!join", "color": "#48E848", "timestamp": 1748547882428, "isMod": false, "isSubscriber": false}, {"id": "nz3N269SAzdmscc6Bk5RL", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr my balls", "color": "#48E848", "timestamp": 1748547994065, "isMod": false, "isSubscriber": false}, {"id": "EKwvXX2Wybc_6WIT15OMB", "username": "nightbot", "message": "@lordwolfyyy -> \"Your Favorite Martian - My Balls [Official Music Video]\" by Your Favorite Martian has been added to the queue in position #2", "color": "#7C7CE1", "timestamp": 1748547994766, "isMod": true, "isSubscriber": false}, {"id": "NlQCbG1vRMvcMa9GxqSyt", "username": "streamelements", "message": "@lordwolfyyy, added Your Favorite Martian - \"Your Favorite Martian - My Balls [Official Music Video]\" to the queue at #4 (playing ~in  10 mins 39 secs) https://youtu.be/6g65f3FbVRY", "color": "#5B99FF", "timestamp": 1748547994820, "isMod": true, "isSubscriber": false}, {"id": "549couz3BI84dj_IWTPrg", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr gay", "color": "#48E848", "timestamp": 1748547996792, "isMod": false, "isSubscriber": false}, {"id": "567mrsqVkoRyN2gDncTWG", "username": "streamelements", "message": "@lordwolfyyy, added al jokes - \"gay vs GAY #shorts #comedy #funny\" to the queue at #5 (playing ~in  13 mins 41 secs) https://youtu.be/PUcH7PruuiA", "color": "#5B99FF", "timestamp": 1748547997512, "isMod": true, "isSubscriber": false}, {"id": "16TdT-rtZ_Ndk76uXNv6s", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr sex", "color": "#48E848", "timestamp": 1748547999735, "isMod": false, "isSubscriber": false}, {"id": "NX-SNA8dbF7SwVNSXCrY4", "username": "nightbot", "message": "@lordwolfyyy -> \"BUENSA – Sex In The City ft. Gat Putch & FRNC$ (Official Music Video)\" by BUENSA has been added to the queue in position #3", "color": "#7C7CE1", "timestamp": 1748548000375, "isMod": true, "isSubscriber": false}, {"id": "JbFq45i-Bs6EdF9SihGyq", "username": "streamelements", "message": "@lordwolfyyy, added <PERSON>clips - \"Black Snake Moan (2007) - Sex Withdrawals Scene | Movieclips\" to the queue at #6 (playing ~in  14 mins) https://youtu.be/ucZbcz6MWws", "color": "#5B99FF", "timestamp": 1748548000403, "isMod": true, "isSubscriber": false}, {"id": "ocw9Snr9HMB2LeCOm9pFx", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr my balls", "color": "#48E848", "timestamp": 1748548001331, "isMod": false, "isSubscriber": false}, {"id": "INVtQWM6SO9XlZSEOhrMe", "username": "streamelements", "message": "@lordwolfyyy, added Your Favorite Martian - \"Your Favorite Martian - My Balls [Official Music Video]\" to the queue at #4 (playing ~in  10 mins 39 secs) https://youtu.be/6g65f3FbVRY", "color": "#5B99FF", "timestamp": 1748548001988, "isMod": true, "isSubscriber": false}, {"id": "_9XB0JoozKgEzYT33Wz9D", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr popular the weekend", "color": "#48E848", "timestamp": 1748548565718, "isMod": false, "isSubscriber": false}, {"id": "lL38-_Vc7mF5DhPoD2Sz1", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748548566259, "isMod": true, "isSubscriber": false}, {"id": "GUwNhJzO_eE_CG83UsBHV", "username": "streamelements", "message": "@lordwolfyyy, added <PERSON>Weeknd<PERSON><PERSON> - \"<PERSON> Weeknd, <PERSON>, <PERSON><PERSON><PERSON> - <PERSON> (Official Music Video)\" to the queue at #8 (playing ~in  19 mins 50 secs) https://youtu.be/vt0i6nuqNEo", "color": "#5B99FF", "timestamp": 1748548566408, "isMod": true, "isSubscriber": false}, {"id": "80hIU0k5fTHahTuz71Qfc", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr bang ajr", "color": "#48E848", "timestamp": 1748549108509, "isMod": false, "isSubscriber": false}, {"id": "OBHrFLNsoCQcL6JWstoWr", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748549108967, "isMod": true, "isSubscriber": false}, {"id": "YXKYQOooHvwPmSdMAyFkJ", "username": "streamelements", "message": "@lordwolfyyy, added 7clouds - \"AJR - BANG! (Lyrics)\" to the queue at #9 (playing ~in  23 mins 41 secs) https://youtu.be/9e2buqBpSBU", "color": "#5B99FF", "timestamp": 1748549109230, "isMod": true, "isSubscriber": false}]}, "metrics": {"totalChatMessages": 30, "totalCommands": 2, "totalSongRequests": 1, "startTime": 1748544496959}, "settings": {}}