{"name": "twitch-bot-app", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint .", "preview": "vite preview", "server": "node --import tsx/esm src/server/index.ts", "dev:all": "concurrently \"npm run dev\" \"npm run server\""}, "dependencies": {"lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.22.3", "tmi.js": "^1.8.5", "socket.io": "^4.7.4", "socket.io-client": "^4.7.4", "express": "^4.18.3", "cors": "^2.8.5", "axios": "^1.6.7", "react-beautiful-dnd": "^13.1.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "tailwind-merge": "^2.2.1", "zustand": "^4.5.2", "nanoid": "^5.0.6", "dotenv": "^16.4.5"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/tmi.js": "^1.8.6", "@types/react-beautiful-dnd": "^13.1.8", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^20.11.24", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "concurrently": "^8.2.2", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "tsx": "^4.7.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}