import { useState } from 'react';
import { 
  Menu, 
  <PERSON>, 
  <PERSON>, 
  Sun, 
  User,
  ChevronDown
} from 'lucide-react';
import { useAuth } from '../hooks/useAuth';

type TopBarProps = {
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
};

const TopBar = ({ sidebarOpen, setSidebarOpen }: TopBarProps) => {
  const { user, logout } = useAuth();
  const [darkMode, setDarkMode] = useState(true);
  const [notificationsOpen, setNotificationsOpen] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);

  const toggleTheme = () => {
    setDarkMode(!darkMode);
    // This would normally toggle classes on the body or apply the theme
  };

  return (
    <header className="bg-gray-800 border-b border-gray-700 h-16 flex items-center px-4 sticky top-0 z-10">
      <button
        className="text-gray-400 hover:text-white mr-4 lg:hidden"
        onClick={() => setSidebarOpen(!sidebarOpen)}
      >
        <Menu size={24} />
      </button>

      <div className="flex-1">
        <div className="relative max-w-md">
          <input
            type="text"
            placeholder="Search commands, users, etc."
            className="input w-full"
          />
        </div>
      </div>

      <div className="flex items-center space-x-4">
        <button
          className="p-2 text-gray-400 hover:text-white rounded-full hover:bg-gray-700"
          onClick={toggleTheme}
        >
          {darkMode ? <Sun size={20} /> : <Moon size={20} />}
        </button>

        <div className="relative">
          <button
            className="p-2 text-gray-400 hover:text-white rounded-full hover:bg-gray-700"
            onClick={() => setNotificationsOpen(!notificationsOpen)}
          >
            <Bell size={20} />
            <span className="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full"></span>
          </button>
          
          {notificationsOpen && (
            <div className="absolute right-0 mt-2 w-80 bg-gray-800 border border-gray-700 rounded-md shadow-lg z-20">
              <div className="p-4 border-b border-gray-700">
                <h3 className="font-bold">Notifications</h3>
              </div>
              <div className="p-2 max-h-96 overflow-y-auto">
                <div className="p-3 hover:bg-gray-700 rounded-md">
                  <p className="text-sm">New subscriber: UserName123</p>
                  <p className="text-xs text-gray-400">2 minutes ago</p>
                </div>
                <div className="p-3 hover:bg-gray-700 rounded-md">
                  <p className="text-sm">Command !giveaway used 10 times</p>
                  <p className="text-xs text-gray-400">15 minutes ago</p>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="relative">
          <button
            className="flex items-center space-x-2 text-gray-300 hover:text-white"
            onClick={() => setUserMenuOpen(!userMenuOpen)}
          >
            <div className="h-8 w-8 rounded-full bg-purple-600 flex items-center justify-center">
              <User size={18} />
            </div>
            <span className="hidden md:block">{user?.username || 'User'}</span>
            <ChevronDown size={16} />
          </button>

          {userMenuOpen && (
            <div className="absolute right-0 mt-2 w-48 bg-gray-800 border border-gray-700 rounded-md shadow-lg z-20">
              <div className="p-2">
                <button className="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 rounded-md">
                  Profile
                </button>
                <button 
                  className="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 rounded-md"
                  onClick={logout}
                >
                  Logout
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default TopBar;