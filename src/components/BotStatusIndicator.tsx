import { useState, useEffect } from 'react';
import { cn } from '../lib/utils';
import { useSocket } from '../hooks/useSocket';
import ConnectionDebug from './ConnectionDebug';

type BotStatusIndicatorProps = {
  connected?: boolean;
};

const BotStatusIndicator = ({ connected: propConnected }: BotStatusIndicatorProps) => {
  const socket = useSocket();
  const [botConnected, setBotConnected] = useState(false);
  const [socketConnected, setSocketConnected] = useState(false);
  const [showDebug, setShowDebug] = useState(false);

  useEffect(() => {
    if (!socket) return;

    setSocketConnected(socket.connected);

    socket.on('connect', () => {
      setSocketConnected(true);
    });

    socket.on('disconnect', () => {
      setSocketConnected(false);
      setBotConnected(false);
    });

    // Check bot status periodically
    const checkBotStatus = async () => {
      if (socket.connected) {
        try {
          const response = await fetch('/api/bot/status');
          const data = await response.json();
          setBotConnected(data.connected);
        } catch (error) {
          setBotConnected(false);
        }
      }
    };

    checkBotStatus();
    const interval = setInterval(checkBotStatus, 5000);

    return () => {
      socket.off('connect');
      socket.off('disconnect');
      clearInterval(interval);
    };
  }, [socket]);

  const connected = propConnected !== undefined ? propConnected : (socketConnected && botConnected);

  return (
    <div className="relative">
      <button
        onClick={() => setShowDebug(!showDebug)}
        className="fixed bottom-4 right-4 z-10 flex items-center space-x-2 bg-gray-800 py-2 px-4 rounded-full shadow-lg border border-gray-700 hover:bg-gray-700 transition-colors cursor-pointer"
      >
        <div
          className={cn(
            "h-3 w-3 rounded-full",
            connected
              ? "bg-green-500 animate-pulse-slow"
              : "bg-red-500"
          )}
        />
        <span className="text-sm font-medium">
          {!socketConnected ? 'Server Offline' :
           !botConnected ? 'Twitch Disconnected' :
           'Bot Online'}
        </span>
        <span className="text-xs text-gray-400 ml-2">
          (debug)
        </span>
      </button>

      {showDebug && (
        <div className="fixed bottom-20 right-4 z-50">
          <div className="relative">
            <button
              onClick={() => setShowDebug(false)}
              className="absolute top-2 right-2 text-gray-400 hover:text-white z-10 bg-gray-800 rounded-full w-6 h-6 flex items-center justify-center"
            >
              ✕
            </button>
            <ConnectionDebug />
          </div>
        </div>
      )}
    </div>
  );
};

export default BotStatusIndicator;