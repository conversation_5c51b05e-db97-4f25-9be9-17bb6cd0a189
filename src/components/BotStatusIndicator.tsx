import { cn } from '../lib/utils';

type BotStatusIndicatorProps = {
  connected: boolean;
};

const BotStatusIndicator = ({ connected }: BotStatusIndicatorProps) => {
  return (
    <div className="fixed bottom-4 right-4 z-10 flex items-center space-x-2 bg-gray-800 py-2 px-4 rounded-full shadow-lg border border-gray-700">
      <div 
        className={cn(
          "h-3 w-3 rounded-full",
          connected 
            ? "bg-green-500 animate-pulse-slow" 
            : "bg-red-500"
        )}
      />
      <span className="text-sm font-medium">
        {connected ? 'Bot Online' : 'Bot Offline'}
      </span>
    </div>
  );
};

export default BotStatusIndicator;