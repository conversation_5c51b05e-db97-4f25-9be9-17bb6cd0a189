import { useState, useEffect } from 'react';
import { useSocket } from '../hooks/useSocket';

const ConnectionDebug = () => {
  const socket = useSocket();
  const [serverStatus, setServerStatus] = useState<'checking' | 'online' | 'offline'>('checking');
  const [socketStatus, setSocketStatus] = useState<string>('disconnected');
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev].slice(0, 10));
  };

  useEffect(() => {
    // Check if server is reachable
    const checkServer = async () => {
      try {
        const response = await fetch('http://localhost:3001/api/bot/status');
        if (response.ok) {
          setServerStatus('online');
          addLog('✅ Server is reachable');
        } else {
          setServerStatus('offline');
          addLog('❌ Server responded with error');
        }
      } catch (error) {
        setServerStatus('offline');
        addLog('❌ Server is not reachable');
      }
    };

    checkServer();
    const interval = setInterval(checkServer, 5000);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    if (!socket) {
      setSocketStatus('no socket instance');
      addLog('❌ No socket instance');
      return;
    }

    addLog(`🔌 Socket instance created. Connected: ${socket.connected}`);
    addLog(`🔌 Socket ID: ${socket.id || 'none'}`);

    const updateStatus = () => {
      const connected = socket.connected;
      const id = socket.id;
      setSocketStatus(connected ? `connected (${id})` : 'disconnected');
      addLog(`📊 Status update: connected=${connected}, id=${id}`);
    };

    updateStatus();

    socket.on('connect', () => {
      setSocketStatus(`connected (${socket.id})`);
      addLog(`✅ Socket connected successfully with ID: ${socket.id}`);
    });

    socket.on('disconnect', (reason) => {
      setSocketStatus(`disconnected: ${reason}`);
      addLog(`❌ Socket disconnected: ${reason}`);
    });

    socket.on('connect_error', (error) => {
      setSocketStatus(`error: ${error.message}`);
      addLog(`🔥 Connection error: ${error.message}`);
    });

    // Test if we can receive events
    socket.on('metrics', (data) => {
      addLog(`📊 Received metrics: ${JSON.stringify(data).substring(0, 50)}...`);
    });

    return () => {
      socket.off('connect');
      socket.off('disconnect');
      socket.off('connect_error');
      socket.off('metrics');
    };
  }, [socket]);

  const testConnection = () => {
    if (socket) {
      addLog('🔄 Testing connection...');
      socket.emit('getMetrics');
      socket.once('metrics', () => {
        addLog('✅ Metrics received - connection working!');
      });
    }
  };

  return (
    <div className="fixed top-4 left-4 bg-gray-800 p-4 rounded-lg shadow-lg border border-gray-700 max-w-md">
      <h3 className="text-white font-bold mb-2">Connection Debug</h3>

      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span className="text-gray-300">Server:</span>
          <span className={`font-medium ${
            serverStatus === 'online' ? 'text-green-400' :
            serverStatus === 'offline' ? 'text-red-400' : 'text-yellow-400'
          }`}>
            {serverStatus}
          </span>
        </div>

        <div className="flex justify-between">
          <span className="text-gray-300">Socket:</span>
          <span className={`font-medium ${
            socketStatus.includes('connected') ? 'text-green-400' : 'text-red-400'
          }`}>
            {socketStatus}
          </span>
        </div>
      </div>

      <button
        onClick={testConnection}
        className="mt-2 px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
      >
        Test Connection
      </button>

      <div className="mt-3">
        <h4 className="text-gray-300 text-xs font-medium mb-1">Recent Logs:</h4>
        <div className="bg-gray-900 p-2 rounded text-xs max-h-32 overflow-y-auto">
          {logs.length > 0 ? (
            logs.map((log, index) => (
              <div key={index} className="text-gray-300 mb-1">
                {log}
              </div>
            ))
          ) : (
            <div className="text-gray-500">No logs yet...</div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ConnectionDebug;
