import { useState, useEffect } from 'react';
import { nanoid } from 'nanoid';

type CommandFormProps = {
  command?: any;
  onSubmit: (command: any) => void;
  onCancel: () => void;
};

const CommandForm = ({ command, onSubmit, onCancel }: CommandFormProps) => {
  const [formData, setFormData] = useState({
    id: '',
    name: '',
    response: '',
    userLevel: 'everyone',
    cooldown: 5,
    enabled: true,
    usageCount: 0
  });

  useEffect(() => {
    if (command) {
      setFormData(command);
    } else {
      setFormData({
        id: nanoid(),
        name: '',
        response: '',
        userLevel: 'everyone',
        cooldown: 5,
        enabled: true,
        usageCount: 0
      });
    }
  }, [command]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? Number(value) : value
    }));
  };

  const handleToggle = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4 border border-gray-700 rounded-lg p-6 bg-gray-800">
      <h2 className="text-xl font-bold mb-4">
        {command ? 'Edit Command' : 'Create New Command'}
      </h2>
      
      <div>
        <label className="block text-gray-300 mb-1" htmlFor="name">
          Command Name (without !)
        </label>
        <input
          type="text"
          id="name"
          name="name"
          value={formData.name}
          onChange={handleChange}
          className="input w-full"
          placeholder="command"
          required
        />
      </div>
      
      <div>
        <label className="block text-gray-300 mb-1" htmlFor="response">
          Response
        </label>
        <textarea
          id="response"
          name="response"
          value={formData.response}
          onChange={handleChange}
          className="input w-full h-24"
          placeholder="The bot will respond with this text"
          required
        />
        <p className="text-gray-400 text-sm mt-1">
          Use {'{user}'} to mention the user who triggered the command
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-gray-300 mb-1" htmlFor="userLevel">
            User Level
          </label>
          <select
            id="userLevel"
            name="userLevel"
            value={formData.userLevel}
            onChange={handleChange}
            className="input w-full"
          >
            <option value="broadcaster">Broadcaster Only</option>
            <option value="moderator">Moderator+</option>
            <option value="vip">VIP+</option>
            <option value="subscriber">Subscribers+</option>
            <option value="regular">Regular Viewers+</option>
            <option value="everyone">Everyone</option>
          </select>
        </div>
        
        <div>
          <label className="block text-gray-300 mb-1" htmlFor="cooldown">
            Cooldown (seconds)
          </label>
          <input
            type="number"
            id="cooldown"
            name="cooldown"
            value={formData.cooldown}
            onChange={handleChange}
            className="input w-full"
            min={0}
            max={3600}
          />
        </div>
      </div>
      
      <div className="flex items-center">
        <input
          type="checkbox"
          id="enabled"
          name="enabled"
          checked={formData.enabled}
          onChange={handleToggle}
          className="h-4 w-4 rounded border-gray-600 text-purple-600 focus:ring-purple-500 bg-gray-700"
        />
        <label className="ml-2 text-gray-300" htmlFor="enabled">
          Command Enabled
        </label>
      </div>
      
      <div className="flex justify-end space-x-3 pt-2">
        <button
          type="button"
          onClick={onCancel}
          className="btn btn-secondary"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="btn btn-primary"
        >
          {command ? 'Update Command' : 'Create Command'}
        </button>
      </div>
    </form>
  );
};

export default CommandForm;