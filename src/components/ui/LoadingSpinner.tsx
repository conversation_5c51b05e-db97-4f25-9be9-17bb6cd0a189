import { cn } from '../../lib/utils';

type LoadingSpinnerProps = {
  fullScreen?: boolean;
  size?: 'sm' | 'md' | 'lg';
};

const LoadingSpinner = ({ 
  fullScreen = false, 
  size = 'md' 
}: LoadingSpinnerProps) => {
  const sizeClasses = {
    sm: 'h-4 w-4 border-2',
    md: 'h-8 w-8 border-3',
    lg: 'h-12 w-12 border-4'
  };

  return (
    <div 
      className={cn(
        "flex items-center justify-center",
        fullScreen ? "fixed inset-0 bg-gray-900 bg-opacity-70 z-50" : "p-4"
      )}
    >
      <div 
        className={cn(
          "border-t-purple-500 border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin",
          sizeClasses[size]
        )}
      />
    </div>
  );
};

export default LoadingSpinner;