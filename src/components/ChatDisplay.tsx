import { useState, useEffect, useRef } from 'react';
import { useSocket } from '../hooks/useSocket';

type ChatMessage = {
  id: string;
  username: string;
  message: string;
  color: string;
  timestamp: number;
  isMod: boolean;
  isSubscriber: boolean;
};

const ChatDisplay = () => {
  const socket = useSocket();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [input, setInput] = useState('');
  const chatEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!socket) return;

    socket.on('chatMessage', (message: ChatMessage) => {
      setMessages(prev => [...prev, message].slice(-100));
    });

    socket.on('chatHistory', (history: ChatMessage[]) => {
      setMessages(history);
    });

    // Get initial chat history
    socket.emit('getChatHistory');

    return () => {
      socket.off('chatMessage');
      socket.off('chatHistory');
    };
  }, [socket]);

  useEffect(() => {
    chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || !socket) return;

    socket.emit('sendChatMessage', input);
    setInput('');
  };

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="flex flex-col h-96">
      <div className="flex-1 overflow-y-auto mb-4 bg-gray-900 rounded-md p-2">
        {messages.length > 0 ? (
          <div className="space-y-2">
            {messages.map((msg) => (
              <div key={msg.id} className="animate-fadeIn">
                <span className="text-gray-400 text-xs mr-2">
                  {formatTime(msg.timestamp)}
                </span>
                {msg.isMod && (
                  <span className="bg-green-700 text-white text-xs px-1 rounded mr-1">MOD</span>
                )}
                {msg.isSubscriber && (
                  <span className="bg-purple-700 text-white text-xs px-1 rounded mr-1">SUB</span>
                )}
                <span style={{ color: msg.color }} className="font-medium mr-2">
                  {msg.username}:
                </span>
                <span>{msg.message}</span>
              </div>
            ))}
            <div ref={chatEndRef} />
          </div>
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            No messages yet
          </div>
        )}
      </div>

      <form onSubmit={handleSendMessage} className="flex space-x-2">
        <input
          type="text"
          value={input}
          onChange={(e) => setInput(e.target.value)}
          placeholder="Type a message as bot"
          className="input flex-1"
        />
        <button type="submit" className="btn btn-primary">
          Send
        </button>
      </form>
    </div>
  );
};

export default ChatDisplay;