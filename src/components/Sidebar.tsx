import { Link, useLocation } from 'react-router-dom';
import { 
  LayoutDashboard, 
  Terminal, 
  Gift, 
  Music, 
  Settings, 
  XCircle,
  TwitchIcon
} from 'lucide-react';
import { cn } from '../lib/utils';

type SidebarProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
};

const Sidebar = ({ open, setOpen }: SidebarProps) => {
  const location = useLocation();

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  const navItems = [
    { path: '/', label: 'Dashboard', icon: <LayoutDashboard size={20} /> },
    { path: '/commands', label: 'Commands', icon: <Terminal size={20} /> },
    { path: '/giveaways', label: 'Giveaways', icon: <Gift size={20} /> },
    { path: '/song-requests', label: 'Song Requests', icon: <Music size={20} /> },
    { path: '/settings', label: 'Settings', icon: <Settings size={20} /> },
  ];

  return (
    <>
      {/* Overlay for mobile */}
      {open && (
        <div 
          className="fixed inset-0 z-20 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setOpen(false)}
        />
      )}

      <aside 
        className={cn(
          "fixed top-0 left-0 z-30 h-full w-64 bg-gray-800 border-r border-gray-700 transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static",
          open ? "translate-x-0" : "-translate-x-full"
        )}
      >
        <div className="flex items-center justify-between h-16 px-4 border-b border-gray-700">
          <Link to="/" className="flex items-center space-x-2">
            <TwitchIcon className="h-6 w-6 text-purple-500" />
            <span className="text-xl font-bold text-white">TwitchBot</span>
          </Link>
          <button 
            className="lg:hidden text-gray-400 hover:text-white"
            onClick={() => setOpen(false)}
          >
            <XCircle size={20} />
          </button>
        </div>

        <nav className="px-4 py-6">
          <ul className="space-y-2">
            {navItems.map((item) => (
              <li key={item.path}>
                <Link
                  to={item.path}
                  className={cn(
                    "flex items-center space-x-3 px-3 py-2 rounded-md transition-colors duration-200",
                    isActive(item.path) 
                      ? "bg-purple-600 text-white" 
                      : "text-gray-300 hover:bg-gray-700 hover:text-white"
                  )}
                >
                  {item.icon}
                  <span>{item.label}</span>
                </Link>
              </li>
            ))}
          </ul>
        </nav>

        <div className="absolute bottom-0 w-full p-4 border-t border-gray-700">
          <div className="text-sm text-gray-400">
            <p>Connected as: StreamerName</p>
            <p className="mt-1">Bot version: 0.1.0</p>
          </div>
        </div>
      </aside>
    </>
  );
};

export default Sidebar;