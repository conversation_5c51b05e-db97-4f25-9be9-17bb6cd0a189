import { useEffect, useState } from 'react';
import { X, User, MessageCircle, Calendar, Crown, Star } from 'lucide-react';

type UserProfileModalProps = {
  isOpen: boolean;
  onClose: () => void;
  userProfile: {
    username: string;
    messages: any[];
    stats: {
      totalMessages: number;
      firstSeen: string;
      lastSeen: string;
      isMod: boolean;
      isSubscriber: boolean;
    };
  } | null;
  onRefresh: (username: string) => void;
};

const UserProfileModal = ({ isOpen, onClose, userProfile, onRefresh }: UserProfileModalProps) => {
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (isOpen && userProfile) {
      // Refresh user data every 3 seconds
      const interval = setInterval(() => {
        onRefresh(userProfile.username);
      }, 3000);
      setRefreshInterval(interval);

      return () => {
        if (interval) clearInterval(interval);
      };
    } else {
      if (refreshInterval) {
        clearInterval(refreshInterval);
        setRefreshInterval(null);
      }
    }
  }, [isOpen, userProfile, onRefresh]);

  if (!isOpen || !userProfile) return null;

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
              <User className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white flex items-center space-x-2">
                <span>{userProfile.username}</span>
                {userProfile.stats.isMod && (
                  <Crown className="w-4 h-4 text-yellow-500" title="Moderator" />
                )}
                {userProfile.stats.isSubscriber && (
                  <Star className="w-4 h-4 text-purple-500" title="Subscriber" />
                )}
              </h2>
              <p className="text-gray-400 text-sm">User Profile</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Stats */}
        <div className="p-6 border-b border-gray-700">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-white">{userProfile.stats.totalMessages}</div>
              <div className="text-sm text-gray-400">Messages</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-white">{userProfile.stats.firstSeen}</div>
              <div className="text-sm text-gray-400">First Seen</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-white">{userProfile.stats.lastSeen}</div>
              <div className="text-sm text-gray-400">Last Seen</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-white">
                {userProfile.stats.isMod ? 'Mod' : userProfile.stats.isSubscriber ? 'Sub' : 'Viewer'}
              </div>
              <div className="text-sm text-gray-400">Status</div>
            </div>
          </div>
        </div>

        {/* Recent Messages */}
        <div className="p-6">
          <div className="flex items-center space-x-2 mb-4">
            <MessageCircle className="w-5 h-5 text-gray-400" />
            <h3 className="text-lg font-semibold text-white">Recent Messages</h3>
            <span className="text-sm text-gray-400">
              (Updates every 3s)
            </span>
          </div>
          
          <div className="space-y-3 max-h-64 overflow-y-auto">
            {userProfile.messages.length > 0 ? (
              userProfile.messages.slice(-20).reverse().map((message, index) => (
                <div key={index} className="bg-gray-700 rounded-lg p-3">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm text-gray-400">
                      {formatDate(message.timestamp)} at {formatTime(message.timestamp)}
                    </span>
                    <div className="flex items-center space-x-1">
                      {message.isMod && (
                        <Crown className="w-3 h-3 text-yellow-500" title="Moderator" />
                      )}
                      {message.isSubscriber && (
                        <Star className="w-3 h-3 text-purple-500" title="Subscriber" />
                      )}
                    </div>
                  </div>
                  <p className="text-white text-sm">{message.message}</p>
                </div>
              ))
            ) : (
              <div className="text-center text-gray-400 py-8">
                <MessageCircle className="w-12 h-12 mx-auto mb-2 opacity-50" />
                <p>No messages found for this user</p>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 bg-gray-700 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default UserProfileModal;
