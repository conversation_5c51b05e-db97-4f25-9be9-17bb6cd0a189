import axios from 'axios';

const API_URL = 'http://localhost:3001';

// Commands API
export const getCommands = async () => {
  const response = await axios.get(`${API_URL}/api/commands`);
  return response.data;
};

export const createCommand = async (command: any) => {
  const response = await axios.post(`${API_URL}/api/commands`, command);
  return response.data;
};

export const updateCommand = async (command: any) => {
  const response = await axios.put(`${API_URL}/api/commands/${command.id}`, command);
  return response.data;
};

export const deleteCommand = async (id: string) => {
  const response = await axios.delete(`${API_URL}/api/commands/${id}`);
  return response.data;
};

// Giveaways API
export const getGiveaways = async () => {
  const response = await axios.get(`${API_URL}/api/giveaways`);
  return response.data;
};

export const startGiveaway = async (giveaway: any) => {
  const response = await axios.post(`${API_URL}/api/giveaways`, giveaway);
  return response.data;
};

export const endGiveaway = async (id: string) => {
  const response = await axios.post(`${API_URL}/api/giveaways/${id}/end`);
  return response.data;
};

export const drawWinner = async (id: string) => {
  const response = await axios.post(`${API_URL}/api/giveaways/${id}/draw`);
  return response.data;
};

// Song Requests API
export const getSongRequests = async () => {
  const response = await axios.get(`${API_URL}/api/songs`);
  return response.data;
};

export const addSong = async (song: any) => {
  const response = await axios.post(`${API_URL}/api/songs`, song);
  return response.data;
};

export const removeSong = async (id: string) => {
  const response = await axios.delete(`${API_URL}/api/songs/${id}`);
  return response.data;
};

export const updateSongQueue = async (queue: any[]) => {
  const response = await axios.put(`${API_URL}/api/songs/queue`, { queue });
  return response.data;
};

// Chat API
export const getChatHistory = async () => {
  const response = await axios.get(`${API_URL}/api/chat/history`);
  return response.data;
};

export const sendChatMessage = async (message: string) => {
  const response = await axios.post(`${API_URL}/api/chat/message`, { message });
  return response.data;
};

// Bot Status API
export const getBotStatus = async () => {
  const response = await axios.get(`${API_URL}/api/bot/status`);
  return response.data;
};

export const getBotMetrics = async () => {
  const response = await axios.get(`${API_URL}/api/bot/metrics`);
  return response.data;
};

// Authentication API
export const login = async (credentials: { username: string; password: string }) => {
  const response = await axios.post(`${API_URL}/api/auth/login`, credentials);
  return response.data;
};

export const logout = async () => {
  const response = await axios.post(`${API_URL}/api/auth/logout`);
  return response.data;
};