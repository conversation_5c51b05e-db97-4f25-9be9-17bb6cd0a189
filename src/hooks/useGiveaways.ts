import { useState, useEffect } from 'react';
import { useSocket } from './useSocket';
import { nanoid } from 'nanoid';

type GiveawayEntry = {
  username: string;
  timestamp: string;
};

type GiveawayWinner = {
  username: string;
  timestamp: string;
};

type ActiveGiveaway = {
  id: string;
  title: string;
  prize: string;
  keyword: string;
  eligibility: string;
  startTime: string;
  endTime: string;
  timeRemaining: string;
  isPaused: boolean;
  entries: GiveawayEntry[];
  winners: GiveawayWinner[];
};

type PastGiveaway = {
  id: string;
  title: string;
  prize: string;
  date: string;
  entriesCount: number;
  winner: string;
};



export const useGiveaways = () => {
  const socket = useSocket();
  const [activeGiveaway, setActiveGiveaway] = useState<ActiveGiveaway | null>(null);
  const [pastGiveaways, setPastGiveaways] = useState<PastGiveaway[]>([]);

  useEffect(() => {
    if (!socket) return;

    socket.on('giveawayUpdate', (data: { active: ActiveGiveaway | null; past: PastGiveaway[] }) => {
      setActiveGiveaway(data.active);
      setPastGiveaways(data.past);
    });

    // Get initial giveaway data
    socket.emit('getGiveaways');

    // Request updates every 3 seconds
    const interval = setInterval(() => {
      socket.emit('getGiveaways');
    }, 3000);

    return () => {
      socket.off('giveawayUpdate');
      clearInterval(interval);
    };
  }, [socket]);

  const startGiveaway = (giveaway: any) => {
    if (socket) {
      socket.emit('startGiveaway', giveaway);
    }
  };

  const endGiveaway = () => {
    if (socket) {
      socket.emit('endGiveaway');
    }
  };

  const drawWinner = () => {
    if (!activeGiveaway || activeGiveaway.entries.length === 0) return;

    // Randomly select a winner from entries
    const randomIndex = Math.floor(Math.random() * activeGiveaway.entries.length);
    const winner = activeGiveaway.entries[randomIndex];

    const winnerEntry: GiveawayWinner = {
      username: winner.username,
      timestamp: new Date().toLocaleTimeString()
    };

    setActiveGiveaway(prev => {
      if (!prev) return null;

      return {
        ...prev,
        winners: [...prev.winners, winnerEntry]
      };
    });

    if (socket) {
      socket.emit('drawWinner', {
        giveawayId: activeGiveaway.id,
        winner: winnerEntry
      });
    }
  };

  const resetGiveaway = () => {
    if (socket) {
      socket.emit('resetGiveaway');
    }
  };

  return {
    activeGiveaway,
    pastGiveaways,
    startGiveaway,
    endGiveaway,
    drawWinner,
    resetGiveaway
  };
};