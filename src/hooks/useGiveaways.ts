import { useState, useEffect } from 'react';
import { useSocket } from './useSocket';
import { nanoid } from 'nanoid';

type GiveawayEntry = {
  username: string;
  timestamp: string;
};

type GiveawayWinner = {
  username: string;
  timestamp: string;
};

type ActiveGiveaway = {
  id: string;
  title: string;
  prize: string;
  keyword: string;
  eligibility: string;
  startTime: string;
  endTime: string;
  timeRemaining: string;
  isPaused: boolean;
  entries: GiveawayEntry[];
  winners: GiveawayWinner[];
};

type PastGiveaway = {
  id: string;
  title: string;
  prize: string;
  date: string;
  entriesCount: number;
  winner: string;
};

// Mock past giveaways
const initialPastGiveaways: PastGiveaway[] = [
  {
    id: nanoid(),
    title: '1000 Followers Celebration',
    prize: 'Steam Gift Card ($20)',
    date: '2023-06-15',
    entriesCount: 145,
    winner: 'ViewerName123'
  },
  {
    id: nanoid(),
    title: 'Monthly Subscriber Giveaway',
    prize: 'Gaming Headset',
    date: '2023-05-28',
    entriesCount: 87,
    winner: 'SubUser456'
  },
  {
    id: nanoid(),
    title: 'Game Launch Party',
    prize: 'Game Key',
    date: '2023-05-10',
    entriesCount: 212,
    winner: 'GamerFan789'
  }
];

export const useGiveaways = () => {
  const socket = useSocket();
  const [activeGiveaway, setActiveGiveaway] = useState<ActiveGiveaway | null>(null);
  const [pastGiveaways, setPastGiveaways] = useState<PastGiveaway[]>(initialPastGiveaways);

  useEffect(() => {
    if (!socket) return;

    socket.on('activeGiveawayUpdate', (giveaway: ActiveGiveaway | null) => {
      setActiveGiveaway(giveaway);
    });

    socket.on('pastGiveawaysUpdate', (giveaways: PastGiveaway[]) => {
      setPastGiveaways(giveaways);
    });

    // Get initial giveaway data
    socket.emit('getGiveaways');

    return () => {
      socket.off('activeGiveawayUpdate');
      socket.off('pastGiveawaysUpdate');
    };
  }, [socket]);

  const startGiveaway = (giveaway: any) => {
    // For demo purposes, we'll create a mock active giveaway
    const newGiveaway: ActiveGiveaway = {
      id: nanoid(),
      title: giveaway.title,
      prize: giveaway.prize,
      keyword: giveaway.keyword,
      eligibility: giveaway.eligibility,
      startTime: new Date().toISOString(),
      endTime: new Date(Date.now() + giveaway.duration * 60 * 1000).toISOString(),
      timeRemaining: `${giveaway.duration}:00`,
      isPaused: false,
      entries: [
        { username: 'Viewer1', timestamp: '2 minutes ago' },
        { username: 'Subscriber123', timestamp: '1 minute ago' },
        { username: 'Follower456', timestamp: 'Just now' },
      ],
      winners: []
    };
    
    setActiveGiveaway(newGiveaway);
    
    if (socket) {
      socket.emit('startGiveaway', newGiveaway);
    }
  };

  const endGiveaway = () => {
    if (!activeGiveaway) return;
    
    // Add to past giveaways
    const pastGiveaway: PastGiveaway = {
      id: activeGiveaway.id,
      title: activeGiveaway.title,
      prize: activeGiveaway.prize,
      date: new Date().toLocaleDateString(),
      entriesCount: activeGiveaway.entries.length,
      winner: activeGiveaway.winners.length > 0 
        ? activeGiveaway.winners[0].username 
        : 'No winner drawn'
    };
    
    setPastGiveaways(prev => [pastGiveaway, ...prev]);
    setActiveGiveaway(null);
    
    if (socket) {
      socket.emit('endGiveaway', {
        giveawayId: activeGiveaway.id,
        pastGiveaway
      });
    }
  };

  const drawWinner = () => {
    if (!activeGiveaway || activeGiveaway.entries.length === 0) return;
    
    // Randomly select a winner from entries
    const randomIndex = Math.floor(Math.random() * activeGiveaway.entries.length);
    const winner = activeGiveaway.entries[randomIndex];
    
    const winnerEntry: GiveawayWinner = {
      username: winner.username,
      timestamp: new Date().toLocaleTimeString()
    };
    
    setActiveGiveaway(prev => {
      if (!prev) return null;
      
      return {
        ...prev,
        winners: [...prev.winners, winnerEntry]
      };
    });
    
    if (socket) {
      socket.emit('drawWinner', {
        giveawayId: activeGiveaway.id,
        winner: winnerEntry
      });
    }
  };

  const resetGiveaway = () => {
    if (!activeGiveaway) return;
    
    setActiveGiveaway(prev => {
      if (!prev) return null;
      
      return {
        ...prev,
        entries: [],
        winners: []
      };
    });
    
    if (socket) {
      socket.emit('resetGiveaway', activeGiveaway.id);
    }
  };

  return {
    activeGiveaway,
    pastGiveaways,
    startGiveaway,
    endGiveaway,
    drawWinner,
    resetGiveaway
  };
};