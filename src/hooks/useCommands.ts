import { useState, useEffect } from 'react';
import { useSocket } from './useSocket';
import { nanoid } from 'nanoid';

type Command = {
  id: string;
  name: string;
  response: string;
  userLevel: string;
  cooldown: number;
  enabled: boolean;
  usageCount: number;
};



export const useCommands = () => {
  const socket = useSocket();
  const [commands, setCommands] = useState<Command[]>([]);

  useEffect(() => {
    if (!socket) return;

    socket.on('commandsUpdate', (updatedCommands: Command[]) => {
      setCommands(updatedCommands);
    });

    // Get initial commands
    socket.emit('getCommands');

    // Request updates every 3 seconds
    const interval = setInterval(() => {
      socket.emit('getCommands');
    }, 3000);

    return () => {
      socket.off('commandsUpdate');
      clearInterval(interval);
    };
  }, [socket]);

  const addCommand = (command: Command) => {
    const newCommand = {
      ...command,
      id: command.id || nanoid(),
      usageCount: 0
    };

    if (socket) {
      socket.emit('addCommand', newCommand);
    }
  };

  const updateCommand = (command: Command) => {
    if (socket) {
      socket.emit('updateCommand', command);
    }
  };

  const deleteCommand = (id: string) => {
    if (socket) {
      socket.emit('deleteCommand', id);
    }
  };

  return {
    commands,
    addCommand,
    updateCommand,
    deleteCommand
  };
};