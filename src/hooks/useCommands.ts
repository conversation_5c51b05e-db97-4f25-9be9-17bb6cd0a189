import { useState, useEffect } from 'react';
import { useSocket } from './useSocket';
import { nanoid } from 'nanoid';

type Command = {
  id: string;
  name: string;
  response: string;
  userLevel: string;
  cooldown: number;
  enabled: boolean;
  usageCount: number;
};

// Mock initial commands
const initialCommands: Command[] = [
  {
    id: nanoid(),
    name: 'discord',
    response: 'Join our Discord server at: https://discord.gg/example',
    userLevel: 'everyone',
    cooldown: 10,
    enabled: true,
    usageCount: 42
  },
  {
    id: nanoid(),
    name: 'uptime',
    response: 'Stream has been live for {uptime}',
    userLevel: 'everyone',
    cooldown: 5,
    enabled: true,
    usageCount: 23
  },
  {
    id: nanoid(),
    name: 'socials',
    response: 'Follow me on Twitter: @twitchstreamer | Instagram: @streamer',
    userLevel: 'everyone',
    cooldown: 15,
    enabled: true,
    usageCount: 18
  },
  {
    id: nanoid(),
    name: 'lurk',
    response: '{user} is now lurking! Enjoy your lurk!',
    userLevel: 'everyone',
    cooldown: 0,
    enabled: true,
    usageCount: 12
  },
  {
    id: nanoid(),
    name: 'clear',
    response: 'Chat has been cleared by a moderator.',
    userLevel: 'moderator',
    cooldown: 0,
    enabled: true,
    usageCount: 5
  }
];

export const useCommands = () => {
  const socket = useSocket();
  const [commands, setCommands] = useState<Command[]>(initialCommands);

  useEffect(() => {
    if (!socket) return;

    socket.on('commandsUpdate', (updatedCommands: Command[]) => {
      setCommands(updatedCommands);
    });

    // Get initial commands
    socket.emit('getCommands');

    return () => {
      socket.off('commandsUpdate');
    };
  }, [socket]);

  const addCommand = (command: Command) => {
    // In a real app, we would emit to the server
    // For this demo, we'll just update the local state
    const newCommand = {
      ...command,
      id: command.id || nanoid(),
      usageCount: 0
    };
    
    setCommands(prev => [...prev, newCommand]);
    
    if (socket) {
      socket.emit('addCommand', newCommand);
    }
  };

  const updateCommand = (command: Command) => {
    setCommands(prev => 
      prev.map(cmd => cmd.id === command.id ? command : cmd)
    );
    
    if (socket) {
      socket.emit('updateCommand', command);
    }
  };

  const deleteCommand = (id: string) => {
    setCommands(prev => prev.filter(cmd => cmd.id !== id));
    
    if (socket) {
      socket.emit('deleteCommand', id);
    }
  };

  return {
    commands,
    addCommand,
    updateCommand,
    deleteCommand
  };
};