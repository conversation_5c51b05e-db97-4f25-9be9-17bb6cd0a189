import { useState, useEffect } from 'react';
import { useSocket } from './useSocket';
import { nanoid } from 'nanoid';

type Song = {
  id: string;
  title: string;
  artist: string;
  thumbnail: string;
  duration: number;
  currentTime?: number;
  requestedBy: string;
  source: string;
  sourceId: string;
};

// Mock songs for the queue
const initialQueue: Song[] = [
  {
    id: nanoid(),
    title: 'Never Gonna Give You Up',
    artist: '<PERSON>',
    thumbnail: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/hqdefault.jpg',
    duration: 213,
    requestedBy: 'ViewerName123',
    source: 'youtube',
    sourceId: 'dQw4w9WgXcQ'
  },
  {
    id: nanoid(),
    title: 'Take On Me',
    artist: 'a-ha',
    thumbnail: 'https://i.ytimg.com/vi/djV11Xbc914/hqdefault.jpg',
    duration: 225,
    requestedBy: 'ModeratorUser',
    source: 'youtube',
    sourceId: 'djV11Xbc914'
  },
  {
    id: nanoid(),
    title: '<PERSON> <PERSON>',
    artist: '<PERSON>',
    thumbnail: 'https://i.ytimg.com/vi/Zi_XLOBDo_Y/hqdefault.jpg',
    duration: 294,
    requestedBy: 'RandomViewer42',
    source: 'youtube',
    sourceId: 'Zi_XLOBDo_Y'
  }
];

// Mock search results
const mockSearchResults: Song[] = [
  {
    id: nanoid(),
    title: 'Bohemian Rhapsody',
    artist: 'Queen',
    thumbnail: 'https://i.ytimg.com/vi/fJ9rUzIMcZQ/hqdefault.jpg',
    duration: 367,
    requestedBy: 'System',
    source: 'youtube',
    sourceId: 'fJ9rUzIMcZQ'
  },
  {
    id: nanoid(),
    title: 'Smells Like Teen Spirit',
    artist: 'Nirvana',
    thumbnail: 'https://i.ytimg.com/vi/hTWKbfoikeg/hqdefault.jpg',
    duration: 301,
    requestedBy: 'System',
    source: 'youtube',
    sourceId: 'hTWKbfoikeg'
  },
  {
    id: nanoid(),
    title: 'Hotel California',
    artist: 'Eagles',
    thumbnail: 'https://i.ytimg.com/vi/EqPtz5qN7HM/hqdefault.jpg',
    duration: 391,
    requestedBy: 'System',
    source: 'youtube',
    sourceId: 'EqPtz5qN7HM'
  },
  {
    id: nanoid(),
    title: 'Sweet Child O\' Mine',
    artist: 'Guns N\' Roses',
    thumbnail: 'https://i.ytimg.com/vi/1w7OgIMMRc4/hqdefault.jpg',
    duration: 356,
    requestedBy: 'System',
    source: 'youtube',
    sourceId: '1w7OgIMMRc4'
  }
];

export const useSongRequests = () => {
  const socket = useSocket();
  const [queue, setQueue] = useState<Song[]>([]);
  const [currentSong, setCurrentSong] = useState<Song & { currentTime: number } | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState(80);
  const [searchResults, setSearchResults] = useState<Song[]>([]);

  useEffect(() => {
    if (!socket) return;

    socket.on('queueUpdate', (updatedQueue: Song[]) => {
      setQueue(updatedQueue);
    });

    socket.on('currentSongUpdate', (song: (Song & { currentTime: number }) | null) => {
      setCurrentSong(song);
    });

    socket.on('playbackUpdate', (playing: boolean) => {
      setIsPlaying(playing);
    });

    socket.on('searchResults', (results: Song[]) => {
      setSearchResults(results);
    });

    // Get initial song request data
    socket.emit('getSongRequests');

    return () => {
      socket.off('queueUpdate');
      socket.off('currentSongUpdate');
      socket.off('playbackUpdate');
      socket.off('searchResults');
    };
  }, [socket]);

  const addSong = (song: Song) => {
    const newSong = {
      ...song,
      id: nanoid(),
      requestedBy: 'You'
    };

    setQueue(prev => [...prev, newSong]);

    if (!currentSong) {
      setCurrentSong({ ...newSong, currentTime: 0 });
      setIsPlaying(true);
    }

    if (socket) {
      socket.emit('addSong', newSong);
    }
  };

  const removeSong = (id: string) => {
    setQueue(prev => prev.filter(song => song.id !== id));

    if (socket) {
      socket.emit('removeSong', id);
    }
  };

  const playSong = (id?: string) => {
    if (id) {
      // Play specific song from queue
      const songToPlay = queue.find(song => song.id === id);

      if (songToPlay) {
        setCurrentSong({ ...songToPlay, currentTime: 0 });
        setQueue(prev => prev.filter(song => song.id !== id));
        setIsPlaying(true);
      }
    } else if (currentSong) {
      // Resume current song
      setIsPlaying(true);
    } else if (queue.length > 0) {
      // Start playing first song in queue
      const nextSong = queue[0];
      setCurrentSong({ ...nextSong, currentTime: 0 });
      setQueue(prev => prev.slice(1));
      setIsPlaying(true);
    }

    if (socket) {
      socket.emit('playSong', id);
    }
  };

  const pauseSong = () => {
    setIsPlaying(false);

    if (socket) {
      socket.emit('pauseSong');
    }
  };

  const skipSong = () => {
    if (queue.length > 0) {
      const nextSong = queue[0];
      setCurrentSong({ ...nextSong, currentTime: 0 });
      setQueue(prev => prev.slice(1));
      setIsPlaying(true);
    } else {
      setCurrentSong(null);
      setIsPlaying(false);
    }

    if (socket) {
      socket.emit('skipSong');
    }
  };

  const reorderQueue = (sourceIndex: number, destinationIndex: number) => {
    const result = Array.from(queue);
    const [removed] = result.splice(sourceIndex, 1);
    result.splice(destinationIndex, 0, removed);

    setQueue(result);

    if (socket) {
      socket.emit('reorderQueue', { sourceIndex, destinationIndex });
    }
  };

  const searchSongs = (term: string) => {
    if (socket) {
      socket.emit('searchSongs', term);
    }
  };

  return {
    queue,
    currentSong,
    isPlaying,
    volume,
    searchResults,
    addSong,
    removeSong,
    playSong,
    pauseSong,
    skipSong,
    setVolume,
    reorderQueue,
    searchSongs
  };
};