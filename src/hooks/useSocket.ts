import { useContext } from 'react';
import { SocketContext } from '../contexts/SocketContext';

export const useSocket = () => {
  const { socket } = useContext(SocketContext);
  
  if (!socket) {
    console.warn('useSocket must be used within a SocketProvider');
  }
  
  return socket;
};

export const useBotStatus = () => {
  const { connected } = useContext(SocketContext);
  return { connected };
};