import { createContext, useState, useEffect, ReactNode } from 'react';

type User = {
  id: string;
  username: string;
  avatar?: string;
};

type AuthContextType = {
  isAuthenticated: boolean;
  user: User | null;
  loading: boolean;
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
};

export const AuthContext = createContext<AuthContextType>({
  isAuthenticated: false,
  user: null,
  loading: true,
  login: async () => {},
  logout: () => {}
});

type AuthProviderProps = {
  children: ReactNode;
};

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check for stored user in localStorage
    const storedUser = localStorage.getItem('twitch_bot_user');
    
    if (storedUser) {
      try {
        const parsedUser = JSON.parse(storedUser);
        setUser(parsedUser);
        setIsAuthenticated(true);
      } catch (error) {
        // Handle invalid stored data
        localStorage.removeItem('twitch_bot_user');
      }
    }
    
    setLoading(false);
  }, []);

  const login = async (username: string, password: string): Promise<void> => {
    // In a real app, this would validate credentials with a backend
    // For demo purposes, we'll simulate authentication
    
    return new Promise((resolve, reject) => {
      // Simulate API call
      setTimeout(() => {
        // Demo login - any credentials work for demo purposes
        if (username && password) {
          const user = {
            id: '123456789',
            username: username,
            avatar: `https://ui-avatars.com/api/?name=${username}&background=random`
          };
          
          setUser(user);
          setIsAuthenticated(true);
          localStorage.setItem('twitch_bot_user', JSON.stringify(user));
          resolve();
        } else {
          reject(new Error('Invalid credentials'));
        }
      }, 1000);
    });
  };

  const logout = () => {
    setUser(null);
    setIsAuthenticated(false);
    localStorage.removeItem('twitch_bot_user');
  };

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        user,
        loading,
        login,
        logout
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};