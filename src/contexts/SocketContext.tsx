import { createContext, useEffect, useState, ReactNode } from 'react';
import { io, Socket } from 'socket.io-client';

type SocketContextType = {
  socket: Socket | null;
  connected: boolean;
};

export const SocketContext = createContext<SocketContextType>({
  socket: null,
  connected: false
});

type SocketProviderProps = {
  children: ReactNode;
};

export const SocketProvider = ({ children }: SocketProviderProps) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [connected, setConnected] = useState(false);

  useEffect(() => {
    // Determine the server URL based on environment
    const getServerUrl = () => {
      if (import.meta.env.DEV) {
        // In development, try to connect to the backend server
        return 'http://localhost:3001';
      } else {
        // In production, use the same origin
        return window.location.origin;
      }
    };

    console.log('🔌 Attempting to connect to:', getServerUrl());

    const socketInstance = io(getServerUrl(), {
      transports: ['polling', 'websocket'],
      timeout: 20000,
      reconnection: true,
      reconnectionDelay: 1000,
      reconnectionAttempts: 5,
      autoConnect: true,
      forceNew: false
    });

    socketInstance.on('connect', () => {
      console.log('✅ Connected to server at', getServerUrl());
      setConnected(true);
    });

    socketInstance.on('disconnect', (reason) => {
      console.log('❌ Disconnected from server:', reason);
      setConnected(false);
    });

    socketInstance.on('connect_error', (error) => {
      console.error('🔥 Connection error:', error.message);
      setConnected(false);
    });

    socketInstance.on('reconnect', (attemptNumber) => {
      console.log('🔄 Reconnected after', attemptNumber, 'attempts');
      setConnected(true);
    });

    socketInstance.on('reconnect_error', (error) => {
      console.error('🔄❌ Reconnection failed:', error.message);
    });

    setSocket(socketInstance);

    return () => {
      if (socketInstance) {
        socketInstance.disconnect();
      }
    };
  }, []);

  return (
    <SocketContext.Provider value={{ socket, connected }}>
      {children}
    </SocketContext.Provider>
  );
};