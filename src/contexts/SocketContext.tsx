import { createContext, useEffect, useState, ReactNode } from 'react';
import { io, Socket } from 'socket.io-client';

type SocketContextType = {
  socket: Socket | null;
  connected: boolean;
};

export const SocketContext = createContext<SocketContextType>({
  socket: null,
  connected: false
});

type SocketProviderProps = {
  children: ReactNode;
};

export const SocketProvider = ({ children }: SocketProviderProps) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [connected, setConnected] = useState(false);

  useEffect(() => {
    // In a real app, we'd connect to a real server
    // For demo purposes, we'll simulate this
    const socketInstance = io('http://localhost:3001', {
      autoConnect: false,
      transports: ['websocket']
    });

    // We're not actually connecting to a server in this demo
    // This just simulates the socket for UI purposes
    // In a real app, you'd call socketInstance.connect()
    setSocket(socketInstance);
    setConnected(true);

    return () => {
      if (socketInstance) {
        socketInstance.disconnect();
      }
    };
  }, []);

  return (
    <SocketContext.Provider value={{ socket, connected }}>
      {children}
    </SocketContext.Provider>
  );
};