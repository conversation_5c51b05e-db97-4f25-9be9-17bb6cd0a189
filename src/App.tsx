import { Suspense, lazy } from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import Layout from './components/Layout';
import LoadingSpinner from './components/ui/LoadingSpinner';

// Lazy load pages to improve initial load time
const Dashboard = lazy(() => import('./pages/Dashboard'));
const Commands = lazy(() => import('./pages/Commands'));
const Giveaways = lazy(() => import('./pages/Giveaways'));
const SongRequests = lazy(() => import('./pages/SongRequests'));
const Settings = lazy(() => import('./pages/Settings'));
const Login = lazy(() => import('./pages/Login'));

function App() {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/login" element={
          <Suspense fallback={<LoadingSpinner fullScreen />}>
            <Login />
          </Suspense>
        } />
        
        <Route path="/" element={<Layout />}>
          <Route index element={
            <Suspense fallback={<LoadingSpinner />}>
              <Dashboard />
            </Suspense>
          } />
          <Route path="commands" element={
            <Suspense fallback={<LoadingSpinner />}>
              <Commands />
            </Suspense>
          } />
          <Route path="giveaways" element={
            <Suspense fallback={<LoadingSpinner />}>
              <Giveaways />
            </Suspense>
          } />
          <Route path="song-requests" element={
            <Suspense fallback={<LoadingSpinner />}>
              <SongRequests />
            </Suspense>
          } />
          <Route path="settings" element={
            <Suspense fallback={<LoadingSpinner />}>
              <Settings />
            </Suspense>
          } />
        </Route>
      </Routes>
    </BrowserRouter>
  );
}

export default App;