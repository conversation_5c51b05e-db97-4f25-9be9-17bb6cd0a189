import { useState } from 'react';
import { 
  Save, 
  AlertCircle,
  Twitch, 
  Youtube, 
  MessageSquare,
  Bell,
  Bot,
  Globe
} from 'lucide-react';

const Settings = () => {
  const [activeTab, setActiveTab] = useState('general');
  
  const tabs = [
    { id: 'general', label: 'General', icon: <Globe size={18} /> },
    { id: 'connections', label: 'Connections', icon: <Twitch size={18} /> },
    { id: 'notifications', label: 'Notifications', icon: <Bell size={18} /> },
    { id: 'chat', label: 'Chat', icon: <MessageSquare size={18} /> },
    { id: 'bot', label: 'Bot Personality', icon: <Bot size={18} /> },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold">Settings</h1>
        <p className="text-gray-400">Configure your bot and stream settings</p>
      </div>

      <div className="card">
        <div className="border-b border-gray-700 mb-6">
          <div className="flex overflow-x-auto">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                className={`flex items-center space-x-2 px-4 py-3 border-b-2 font-medium whitespace-nowrap ${
                  activeTab === tab.id 
                    ? 'border-purple-500 text-purple-500' 
                    : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-600'
                }`}
                onClick={() => setActiveTab(tab.id)}
              >
                {tab.icon}
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {activeTab === 'general' && (
          <div className="space-y-6">
            <div>
              <h2 className="text-lg font-bold mb-4">General Settings</h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-gray-300 mb-1" htmlFor="bot-name">
                    Bot Name
                  </label>
                  <input
                    type="text"
                    id="bot-name"
                    defaultValue="TwitchBot"
                    className="input w-full"
                  />
                </div>
                
                <div>
                  <label className="block text-gray-300 mb-1" htmlFor="prefix">
                    Command Prefix
                  </label>
                  <input
                    type="text"
                    id="prefix"
                    defaultValue="!"
                    className="input w-full"
                  />
                </div>
                
                <div>
                  <label className="block text-gray-300 mb-1" htmlFor="timezone">
                    Timezone
                  </label>
                  <select
                    id="timezone"
                    className="input w-full"
                    defaultValue="UTC"
                  >
                    <option value="UTC">UTC</option>
                    <option value="America/New_York">Eastern Time (ET)</option>
                    <option value="America/Chicago">Central Time (CT)</option>
                    <option value="America/Denver">Mountain Time (MT)</option>
                    <option value="America/Los_Angeles">Pacific Time (PT)</option>
                    <option value="Europe/London">London (GMT)</option>
                    <option value="Europe/Paris">Central European Time (CET)</option>
                  </select>
                </div>
              </div>
            </div>
            
            <div>
              <h2 className="text-lg font-bold mb-4">Stream Settings</h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-gray-300 mb-1" htmlFor="stream-title">
                    Default Stream Title
                  </label>
                  <input
                    type="text"
                    id="stream-title"
                    placeholder="Enter your default stream title"
                    className="input w-full"
                  />
                </div>
                
                <div>
                  <label className="block text-gray-300 mb-1" htmlFor="stream-category">
                    Default Category
                  </label>
                  <input
                    type="text"
                    id="stream-category"
                    placeholder="E.g. Just Chatting, Valorant, etc."
                    className="input w-full"
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <label className="text-gray-300" htmlFor="auto-shoutout">
                    Auto Shoutout Raiders
                  </label>
                  <input
                    type="checkbox"
                    id="auto-shoutout"
                    defaultChecked={true}
                    className="h-4 w-4 rounded border-gray-600 text-purple-600 focus:ring-purple-500 bg-gray-700"
                  />
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'connections' && (
          <div className="space-y-6">
            <div>
              <h2 className="text-lg font-bold mb-4">Twitch Connection</h2>
              <div className="p-4 border border-purple-500 bg-purple-500 bg-opacity-10 rounded-lg mb-4">
                <div className="flex items-start">
                  <Twitch className="text-purple-500 mt-1 mr-3" size={20} />
                  <div>
                    <h3 className="font-medium">Connected to Twitch</h3>
                    <p className="text-sm text-gray-400">Account: StreamerName</p>
                  </div>
                </div>
              </div>
              <button className="btn btn-secondary">Reconnect Twitch</button>
            </div>
            
            <div>
              <h2 className="text-lg font-bold mb-4">YouTube Connection</h2>
              <div className="p-4 border border-gray-600 bg-gray-800 rounded-lg mb-4">
                <div className="flex items-start">
                  <Youtube className="text-red-500 mt-1 mr-3" size={20} />
                  <div>
                    <h3 className="font-medium">Not Connected</h3>
                    <p className="text-sm text-gray-400">
                      Connect your YouTube account for song requests and video functionality.
                    </p>
                  </div>
                </div>
              </div>
              <button className="btn btn-primary">Connect YouTube</button>
            </div>
            
            <div>
              <h2 className="text-lg font-bold mb-4">API Keys</h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-gray-300 mb-1" htmlFor="youtube-api">
                    YouTube API Key
                  </label>
                  <div className="flex space-x-2">
                    <input
                      type="password"
                      id="youtube-api"
                      placeholder="Enter your YouTube API key"
                      className="input flex-1"
                    />
                    <button className="btn btn-secondary">Show</button>
                  </div>
                  <p className="text-xs text-gray-400 mt-1">
                    Used for song requests and YouTube search functionality.
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'notifications' && (
          <div className="space-y-6">
            <div>
              <h2 className="text-lg font-bold mb-4">Alert Settings</h2>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <label className="text-gray-300" htmlFor="follow-alerts">
                    Follow Alerts
                  </label>
                  <input
                    type="checkbox"
                    id="follow-alerts"
                    defaultChecked={true}
                    className="h-4 w-4 rounded border-gray-600 text-purple-600 focus:ring-purple-500 bg-gray-700"
                  />
                </div>
                
                <div>
                  <label className="block text-gray-300 mb-1" htmlFor="follow-message">
                    Follow Alert Message
                  </label>
                  <textarea
                    id="follow-message"
                    defaultValue="Thanks for the follow, {user}!"
                    className="input w-full h-20"
                  />
                  <p className="text-xs text-gray-400 mt-1">
                    Use {'{user}'} to include the username in the message.
                  </p>
                </div>
                
                <div className="flex items-center justify-between">
                  <label className="text-gray-300" htmlFor="sub-alerts">
                    Subscription Alerts
                  </label>
                  <input
                    type="checkbox"
                    id="sub-alerts"
                    defaultChecked={true}
                    className="h-4 w-4 rounded border-gray-600 text-purple-600 focus:ring-purple-500 bg-gray-700"
                  />
                </div>
                
                <div>
                  <label className="block text-gray-300 mb-1" htmlFor="sub-message">
                    Subscription Alert Message
                  </label>
                  <textarea
                    id="sub-message"
                    defaultValue="Welcome to the community, {user}! Thanks for subscribing!"
                    className="input w-full h-20"
                  />
                </div>
              </div>
            </div>
            
            <div>
              <h2 className="text-lg font-bold mb-4">Timers</h2>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <label className="text-gray-300" htmlFor="timer1">
                      Social Media Reminder
                    </label>
                    <input
                      type="checkbox"
                      id="timer1"
                      defaultChecked={true}
                      className="h-4 w-4 rounded border-gray-600 text-purple-600 focus:ring-purple-500 bg-gray-700"
                    />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-gray-300 text-sm mb-1" htmlFor="timer1-interval">
                        Interval (minutes)
                      </label>
                      <input
                        type="number"
                        id="timer1-interval"
                        defaultValue={30}
                        min={1}
                        className="input w-full"
                      />
                    </div>
                    <div>
                      <label className="block text-gray-300 text-sm mb-1" htmlFor="timer1-message">
                        Message
                      </label>
                      <textarea
                        id="timer1-message"
                        defaultValue="Don't forget to follow me on Twitter @username!"
                        className="input w-full"
                        rows={2}
                      />
                    </div>
                  </div>
                </div>
                
                <button className="btn btn-secondary">Add New Timer</button>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'chat' && (
          <div className="space-y-6">
            <div>
              <h2 className="text-lg font-bold mb-4">Chat Moderation</h2>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <label className="text-gray-300" htmlFor="links-filter">
                    Filter Links
                  </label>
                  <input
                    type="checkbox"
                    id="links-filter"
                    defaultChecked={true}
                    className="h-4 w-4 rounded border-gray-600 text-purple-600 focus:ring-purple-500 bg-gray-700"
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <label className="text-gray-300" htmlFor="caps-filter">
                    Excessive Caps Filter
                  </label>
                  <input
                    type="checkbox"
                    id="caps-filter"
                    defaultChecked={true}
                    className="h-4 w-4 rounded border-gray-600 text-purple-600 focus:ring-purple-500 bg-gray-700"
                  />
                </div>
                
                <div>
                  <label className="block text-gray-300 mb-1" htmlFor="banned-words">
                    Banned Words/Phrases
                  </label>
                  <textarea
                    id="banned-words"
                    placeholder="Enter words or phrases, one per line"
                    className="input w-full h-24"
                  />
                </div>
              </div>
            </div>
            
            <div>
              <h2 className="text-lg font-bold mb-4">Auto Responses</h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-gray-300 mb-1">
                    Chat Greetings
                  </label>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-400">
                      Automatically greet first-time chatters
                    </span>
                    <input
                      type="checkbox"
                      defaultChecked={true}
                      className="h-4 w-4 rounded border-gray-600 text-purple-600 focus:ring-purple-500 bg-gray-700"
                    />
                  </div>
                  <input
                    type="text"
                    defaultValue="Welcome to the stream, {user}! Hope you enjoy your stay!"
                    className="input w-full"
                  />
                </div>
                
                <div>
                  <label className="block text-gray-300 mb-1">
                    Raid Response
                  </label>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-400">
                      Automatically thank raids
                    </span>
                    <input
                      type="checkbox"
                      defaultChecked={true}
                      className="h-4 w-4 rounded border-gray-600 text-purple-600 focus:ring-purple-500 bg-gray-700"
                    />
                  </div>
                  <input
                    type="text"
                    defaultValue="Thanks for the raid, {raider}! Everyone, check out their channel at twitch.tv/{raider}!"
                    className="input w-full"
                  />
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'bot' && (
          <div className="space-y-6">
            <div>
              <h2 className="text-lg font-bold mb-4">Bot Personality</h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-gray-300 mb-1" htmlFor="bot-personality">
                    Personality Type
                  </label>
                  <select
                    id="bot-personality"
                    className="input w-full"
                    defaultValue="friendly"
                  >
                    <option value="friendly">Friendly & Helpful</option>
                    <option value="professional">Professional</option>
                    <option value="funny">Funny & Entertaining</option>
                    <option value="sassy">Sassy & Witty</option>
                    <option value="minimal">Minimal & To-the-point</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-gray-300 mb-1" htmlFor="custom-responses">
                    Custom Responses
                  </label>
                  <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
                    <div className="mb-3">
                      <label className="block text-sm text-gray-400 mb-1">
                        When someone says hello:
                      </label>
                      <input
                        type="text"
                        defaultValue="Hey there, {user}! How are you doing today?"
                        className="input w-full"
                      />
                    </div>
                    
                    <div className="mb-3">
                      <label className="block text-sm text-gray-400 mb-1">
                        When someone asks what game this is:
                      </label>
                      <input
                        type="text"
                        defaultValue="We're currently playing {game}! Thanks for asking."
                        className="input w-full"
                      />
                    </div>
                    
                    <button className="btn btn-secondary text-sm">
                      Add Custom Response
                    </button>
                  </div>
                </div>
                
                <div className="p-4 border border-yellow-500 bg-yellow-500 bg-opacity-10 rounded-lg">
                  <div className="flex items-start">
                    <AlertCircle className="text-yellow-500 mt-1 mr-3 flex-shrink-0" size={20} />
                    <div>
                      <h3 className="font-medium text-yellow-500">AI Response Feature</h3>
                      <p className="text-sm text-gray-300">
                        Advanced AI-powered chat responses are coming soon! Connect an AI service to enable natural conversation with your viewers.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="flex justify-end mt-6 pt-4 border-t border-gray-700">
          <button className="btn btn-primary flex items-center space-x-2">
            <Save size={18} />
            <span>Save Settings</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default Settings;