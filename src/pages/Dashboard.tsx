import { useState, useEffect } from 'react';
import { useSocket } from '../hooks/useSocket';
import { Clock, Users, MessageSquare, Bell } from 'lucide-react';
import ChatDisplay from '../components/ChatDisplay';
import { useCommands } from '../hooks/useCommands';
import { useAuth } from '../hooks/useAuth';

const Dashboard = () => {
  const socket = useSocket();
  const { user } = useAuth();
  const { commands } = useCommands();
  const [metrics, setMetrics] = useState({
    chatMessages: 0,
    viewers: 0,
    commandsUsed: 0,
    uptime: '00:00:00'
  });
  const [recentEvents, setRecentEvents] = useState<string[]>([]);

  useEffect(() => {
    if (!socket) return;

    // Subscribe to metrics updates
    socket.on('metrics', (data) => {
      setMetrics(data);
    });

    // Subscribe to events
    socket.on('recentEvents', (events) => {
      setRecentEvents(events);
    });

    // Get initial data
    socket.emit('getMetrics');
    socket.emit('getRecentEvents');

    return () => {
      socket.off('metrics');
      socket.off('recentEvents');
    };
  }, [socket]);

  const statCards = [
    {
      title: 'Current Viewers',
      value: metrics.viewers,
      icon: <Users className="text-purple-500\" size={24} />,
      change: '+12%'
    },
    {
      title: 'Chat Messages',
      value: metrics.chatMessages,
      icon: <MessageSquare className="text-blue-500" size={24} />,
      change: '+5%'
    },
    {
      title: 'Commands Used',
      value: metrics.commandsUsed,
      icon: <Bell className="text-green-500\" size={24} />,
      change: '+20%'
    },
    {
      title: 'Stream Uptime',
      value: metrics.uptime,
      icon: <Clock className="text-orange-500" size={24} />,
      change: ''
    }
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold">Dashboard</h1>
        <p className="text-gray-400">Welcome back, {user?.username || 'Streamer'}!</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {statCards.map((stat, index) => (
          <div key={index} className="card flex items-center">
            <div className="mr-4">{stat.icon}</div>
            <div>
              <p className="text-gray-400 text-sm">{stat.title}</p>
              <p className="text-2xl font-bold">{stat.value}</p>
              {stat.change && (
                <p className="text-xs text-green-500">{stat.change} from last stream</p>
              )}
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <div className="card h-full">
            <h2 className="font-bold mb-4">Live Chat</h2>
            <ChatDisplay />
          </div>
        </div>

        <div>
          <div className="card h-full">
            <h2 className="font-bold mb-4">Recent Events</h2>
            <div className="space-y-2 max-h-80 overflow-y-auto">
              {recentEvents.length > 0 ? (
                recentEvents.map((event, i) => (
                  <div key={i} className="p-2 border-b border-gray-700 text-sm">
                    {event}
                  </div>
                ))
              ) : (
                <p className="text-gray-400 text-sm">No recent events</p>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="card">
          <h2 className="font-bold mb-4">Top Commands</h2>
          <div className="space-y-2">
            {commands.slice(0, 5).map((cmd, i) => (
              <div key={i} className="flex justify-between p-2 bg-gray-700 rounded">
                <span>!{cmd.name}</span>
                <span className="text-gray-300">{cmd.usageCount || 0} uses</span>
              </div>
            ))}
          </div>
        </div>

        <div className="card">
          <h2 className="font-bold mb-4">Quick Actions</h2>
          <div className="grid grid-cols-2 gap-3">
            <button className="btn btn-primary">Start Giveaway</button>
            <button className="btn btn-primary">New Command</button>
            <button className="btn btn-primary">Clear Chat</button>
            <button className="btn btn-primary">Mod Tools</button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;