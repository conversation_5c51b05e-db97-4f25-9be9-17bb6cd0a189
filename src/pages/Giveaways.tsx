import { useState } from 'react';
import { 
  Gift, 
  Users, 
  Clock, 
  PlayCircle, 
  PauseCircle,
  RefreshCw
} from 'lucide-react';
import { useGiveaways } from '../hooks/useGiveaways';

const Giveaways = () => {
  const { 
    activeGiveaway, 
    pastGiveaways, 
    startGiveaway, 
    endGiveaway, 
    drawWinner,
    resetGiveaway
  } = useGiveaways();
  
  const [newGiveaway, setNewGiveaway] = useState({
    title: '',
    prize: '',
    duration: 5,
    keyword: '!join',
    eligibility: 'everyone'
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    setNewGiveaway(prev => ({
      ...prev,
      [name]: type === 'number' ? Number(value) : value
    }));
  };

  const handleStartGiveaway = (e: React.FormEvent) => {
    e.preventDefault();
    startGiveaway(newGiveaway);
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold">Giveaways</h1>
        <p className="text-gray-400">Run exciting giveaways for your viewers</p>
      </div>

      {activeGiveaway ? (
        <div className="card border-purple-500">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-4">
            <div>
              <div className="flex items-center space-x-2 mb-2">
                <Gift size={24} className="text-purple-500" />
                <h2 className="text-xl font-bold">Active Giveaway</h2>
              </div>
              <h3 className="text-2xl mb-1">{activeGiveaway.title}</h3>
              <p className="text-gray-400">Prize: {activeGiveaway.prize}</p>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="flex flex-col items-center">
                <p className="text-sm text-gray-400">Time Remaining</p>
                <p className="text-2xl font-bold">
                  {activeGiveaway.timeRemaining || '--:--'}
                </p>
              </div>
              
              <div className="flex flex-col items-center">
                <p className="text-sm text-gray-400">Entries</p>
                <p className="text-2xl font-bold">{activeGiveaway.entries.length}</p>
              </div>
              
              <div className="flex items-center space-x-2">
                <button 
                  className="p-2 rounded-full hover:bg-gray-700 text-gray-300 hover:text-white"
                  title={activeGiveaway.isPaused ? "Resume Giveaway" : "Pause Giveaway"}
                >
                  {activeGiveaway.isPaused ? 
                    <PlayCircle size={24} /> : 
                    <PauseCircle size={24} />
                  }
                </button>
                <button 
                  className="p-2 rounded-full hover:bg-gray-700 text-gray-300 hover:text-white"
                  title="Reset Giveaway"
                  onClick={resetGiveaway}
                >
                  <RefreshCw size={24} />
                </button>
              </div>
            </div>
          </div>
          
          <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-bold mb-3">Entries</h3>
              <div className="bg-gray-900 h-48 rounded-md p-3 overflow-y-auto">
                {activeGiveaway.entries.length > 0 ? (
                  <div className="space-y-1">
                    {activeGiveaway.entries.map((entry, i) => (
                      <div key={i} className="flex justify-between items-center p-2 hover:bg-gray-800 rounded">
                        <span>{entry.username}</span>
                        <span className="text-xs text-gray-400">{entry.timestamp}</span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-500">
                    No entries yet
                  </div>
                )}
              </div>
              <p className="text-sm text-gray-400 mt-2">
                Entry keyword: <span className="font-mono bg-gray-800 px-1 rounded">{activeGiveaway.keyword}</span>
              </p>
            </div>
            
            <div>
              <h3 className="font-bold mb-3">Winners</h3>
              <div className="bg-gray-900 h-48 rounded-md p-3 overflow-y-auto">
                {activeGiveaway.winners.length > 0 ? (
                  <div className="space-y-1">
                    {activeGiveaway.winners.map((winner, i) => (
                      <div key={i} className="flex justify-between items-center p-2 bg-purple-900 bg-opacity-30 rounded">
                        <div className="flex items-center">
                          <Gift size={16} className="text-purple-400 mr-2" />
                          <span>{winner.username}</span>
                        </div>
                        <span className="text-xs text-gray-400">{winner.timestamp}</span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-500">
                    No winners yet
                  </div>
                )}
              </div>
              
              <div className="flex space-x-3 mt-4">
                <button 
                  className="btn btn-primary flex-1"
                  onClick={drawWinner}
                >
                  Draw Winner
                </button>
                <button 
                  className="btn btn-danger"
                  onClick={endGiveaway}
                >
                  End Giveaway
                </button>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="card">
          <h2 className="text-xl font-bold mb-4">Start a New Giveaway</h2>
          
          <form onSubmit={handleStartGiveaway} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-gray-300 mb-1" htmlFor="title">
                  Giveaway Title
                </label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  value={newGiveaway.title}
                  onChange={handleChange}
                  className="input w-full"
                  placeholder="Example: 1000 Subscribers Celebration!"
                  required
                />
              </div>
              
              <div>
                <label className="block text-gray-300 mb-1" htmlFor="prize">
                  Prize
                </label>
                <input
                  type="text"
                  id="prize"
                  name="prize"
                  value={newGiveaway.prize}
                  onChange={handleChange}
                  className="input w-full"
                  placeholder="Example: Steam Gift Card"
                  required
                />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-gray-300 mb-1" htmlFor="duration">
                  Duration (minutes)
                </label>
                <input
                  type="number"
                  id="duration"
                  name="duration"
                  value={newGiveaway.duration}
                  onChange={handleChange}
                  className="input w-full"
                  min={1}
                  max={60}
                  required
                />
              </div>
              
              <div>
                <label className="block text-gray-300 mb-1" htmlFor="keyword">
                  Entry Keyword
                </label>
                <input
                  type="text"
                  id="keyword"
                  name="keyword"
                  value={newGiveaway.keyword}
                  onChange={handleChange}
                  className="input w-full"
                  placeholder="Example: !join"
                  required
                />
              </div>
              
              <div>
                <label className="block text-gray-300 mb-1" htmlFor="eligibility">
                  Eligibility
                </label>
                <select
                  id="eligibility"
                  name="eligibility"
                  value={newGiveaway.eligibility}
                  onChange={handleChange}
                  className="input w-full"
                >
                  <option value="everyone">Everyone</option>
                  <option value="subscribers">Subscribers Only</option>
                  <option value="followers">Followers Only</option>
                  <option value="regulars">Regular Viewers</option>
                </select>
              </div>
            </div>
            
            <div className="flex justify-end">
              <button type="submit" className="btn btn-primary flex items-center space-x-2">
                <Gift size={18} />
                <span>Start Giveaway</span>
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="card">
        <h2 className="text-xl font-bold mb-4">Past Giveaways</h2>
        
        {pastGiveaways.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-700">
                  <th className="text-left py-3 px-4">Title</th>
                  <th className="text-left py-3 px-4">Prize</th>
                  <th className="text-left py-3 px-4">Date</th>
                  <th className="text-left py-3 px-4">Entries</th>
                  <th className="text-left py-3 px-4">Winner</th>
                </tr>
              </thead>
              <tbody>
                {pastGiveaways.map((giveaway, i) => (
                  <tr key={i} className="border-b border-gray-700 hover:bg-gray-800">
                    <td className="py-3 px-4">{giveaway.title}</td>
                    <td className="py-3 px-4">{giveaway.prize}</td>
                    <td className="py-3 px-4">{giveaway.date}</td>
                    <td className="py-3 px-4">{giveaway.entriesCount}</td>
                    <td className="py-3 px-4">{giveaway.winner}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-8 text-gray-400">
            <p>No past giveaways. Start your first giveaway!</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Giveaways;