import { useState } from 'react';
import { useNavigate, Navigate } from 'react-router-dom';
import { TwitchIcon, UserCheck, Lock } from 'lucide-react';
import { useAuth } from '../hooks/useAuth';

const Login = () => {
  const { isAuthenticated, login } = useAuth();
  const navigate = useNavigate();
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);
    
    try {
      await login(username, password);
      navigate('/');
    } catch (err) {
      setError('Invalid credentials. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleDemoLogin = async () => {
    setIsLoading(true);
    
    try {
      await login('demouser', 'demopassword');
      navigate('/');
    } catch (err) {
      setError('Error logging in with demo account');
    } finally {
      setIsLoading(false);
    }
  };
  
  if (isAuthenticated) {
    return <Navigate to="/" replace />;
  }

  return (
    <div className="min-h-screen bg-gray-900 flex flex-col justify-center items-center p-4">
      <div className="max-w-md w-full bg-gray-800 rounded-lg shadow-lg p-8 border border-gray-700">
        <div className="flex justify-center mb-6">
          <div className="bg-purple-600 h-16 w-16 rounded-full flex items-center justify-center">
            <TwitchIcon className="h-10 w-10 text-white" />
          </div>
        </div>
        
        <h1 className="text-2xl font-bold text-center mb-2">Welcome to TwitchBot</h1>
        <p className="text-gray-400 text-center mb-6">
          Sign in to access your Twitch bot dashboard
        </p>
        
        {error && (
          <div className="bg-red-900 bg-opacity-30 border border-red-500 text-red-300 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-gray-300 mb-1" htmlFor="username">
              Username
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <UserCheck size={18} className="text-gray-500" />
              </div>
              <input
                type="text"
                id="username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className="input w-full pl-10"
                placeholder="Enter your username"
                required
              />
            </div>
          </div>
          
          <div>
            <label className="block text-gray-300 mb-1" htmlFor="password">
              Password
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Lock size={18} className="text-gray-500" />
              </div>
              <input
                type="password"
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="input w-full pl-10"
                placeholder="Enter your password"
                required
              />
            </div>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                id="remember-me"
                name="remember-me"
                type="checkbox"
                className="h-4 w-4 rounded border-gray-600 text-purple-600 focus:ring-purple-500 bg-gray-700"
              />
              <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-300">
                Remember me
              </label>
            </div>
            
            <a href="#" className="text-sm text-purple-400 hover:text-purple-300">
              Forgot password?
            </a>
          </div>
          
          <button
            type="submit"
            className="btn btn-primary w-full"
            disabled={isLoading}
          >
            {isLoading ? 'Signing in...' : 'Sign in'}
          </button>
        </form>
        
        <div className="mt-6">
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-700"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-gray-800 text-gray-400">Or continue with</span>
            </div>
          </div>
          
          <div className="mt-6">
            <button
              type="button"
              className="w-full flex items-center justify-center btn bg-purple-700 hover:bg-purple-800 text-white"
              onClick={handleDemoLogin}
            >
              <TwitchIcon className="h-5 w-5 mr-2" />
              Connect with Twitch
            </button>
          </div>
          
          <div className="mt-4">
            <button
              type="button"
              className="w-full btn btn-secondary"
              onClick={handleDemoLogin}
            >
              Try Demo Account
            </button>
          </div>
        </div>
        
        <p className="mt-6 text-center text-sm text-gray-400">
          Don't have an account?{' '}
          <a href="#" className="text-purple-400 hover:text-purple-300 font-medium">
            Sign up
          </a>
        </p>
      </div>
    </div>
  );
};

export default Login;