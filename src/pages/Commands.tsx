import { useState } from 'react';
import { Plus, Edit, Trash, Clock, Shield, User } from 'lucide-react';
import { useCommands } from '../hooks/useCommands';
import CommandForm from '../components/CommandForm';

const Commands = () => {
  const { commands, addCommand, updateCommand, deleteCommand } = useCommands();
  const [showForm, setShowForm] = useState(false);
  const [editingCommand, setEditingCommand] = useState<any>(null);
  const [filter, setFilter] = useState('all');

  const handleAddCommand = () => {
    setEditingCommand(null);
    setShowForm(true);
  };

  const handleEditCommand = (command: any) => {
    setEditingCommand(command);
    setShowForm(true);
  };

  const handleSubmit = (command: any) => {
    if (editingCommand) {
      updateCommand(command);
    } else {
      addCommand(command);
    }
    setShowForm(false);
  };

  const handleCancel = () => {
    setShowForm(false);
    setEditingCommand(null);
  };

  const filteredCommands = commands.filter(cmd => {
    if (filter === 'all') return true;
    if (filter === 'mod' && cmd.userLevel === 'moderator') return true;
    if (filter === 'regular' && cmd.userLevel === 'regular') return true;
    if (filter === 'subscriber' && cmd.userLevel === 'subscriber') return true;
    if (filter === 'everyone' && cmd.userLevel === 'everyone') return true;
    return false;
  });

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">Commands</h1>
          <p className="text-gray-400">Manage your bot commands</p>
        </div>
        
        <button 
          className="btn btn-primary flex items-center space-x-2"
          onClick={handleAddCommand}
        >
          <Plus size={18} />
          <span>Add Command</span>
        </button>
      </div>

      <div className="card">
        <div className="mb-6 flex flex-col sm:flex-row items-center justify-between gap-4">
          <div className="flex space-x-2 overflow-x-auto pb-2 sm:pb-0 w-full sm:w-auto">
            <button 
              className={`px-3 py-1 rounded-md text-sm ${filter === 'all' ? 'bg-purple-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}`}
              onClick={() => setFilter('all')}
            >
              All
            </button>
            <button 
              className={`px-3 py-1 rounded-md text-sm flex items-center space-x-1 ${filter === 'mod' ? 'bg-purple-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}`}
              onClick={() => setFilter('mod')}
            >
              <Shield size={14} />
              <span>Mod Only</span>
            </button>
            <button 
              className={`px-3 py-1 rounded-md text-sm ${filter === 'subscriber' ? 'bg-purple-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}`}
              onClick={() => setFilter('subscriber')}
            >
              Subscriber
            </button>
            <button 
              className={`px-3 py-1 rounded-md text-sm ${filter === 'everyone' ? 'bg-purple-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}`}
              onClick={() => setFilter('everyone')}
            >
              Everyone
            </button>
          </div>

          <div className="w-full sm:w-auto">
            <input
              type="text"
              placeholder="Search commands..."
              className="input w-full sm:w-64"
            />
          </div>
        </div>

        {showForm ? (
          <CommandForm 
            command={editingCommand} 
            onSubmit={handleSubmit} 
            onCancel={handleCancel} 
          />
        ) : (
          <>
            {filteredCommands.length > 0 ? (
              <div className="grid gap-4 grid-cols-1 lg:grid-cols-2">
                {filteredCommands.map((command) => (
                  <div 
                    key={command.id} 
                    className="bg-gray-700 rounded-lg p-4 border border-gray-600"
                  >
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-bold text-lg">!{command.name}</h3>
                      <div className="flex space-x-2">
                        <button 
                          className="p-1 text-gray-400 hover:text-white rounded hover:bg-gray-600"
                          onClick={() => handleEditCommand(command)}
                        >
                          <Edit size={18} />
                        </button>
                        <button 
                          className="p-1 text-gray-400 hover:text-red-500 rounded hover:bg-gray-600"
                          onClick={() => deleteCommand(command.id)}
                        >
                          <Trash size={18} />
                        </button>
                      </div>
                    </div>
                    
                    <p className="text-gray-300 mb-3">{command.response}</p>
                    
                    <div className="flex flex-wrap gap-2 mt-2">
                      <span className="flex items-center text-xs bg-gray-800 text-gray-300 px-2 py-1 rounded">
                        <Clock size={12} className="mr-1" />
                        {command.cooldown}s cooldown
                      </span>
                      <span className="flex items-center text-xs bg-gray-800 text-gray-300 px-2 py-1 rounded">
                        <User size={12} className="mr-1" />
                        {command.userLevel}
                      </span>
                      {command.enabled ? (
                        <span className="text-xs bg-green-900 text-green-300 px-2 py-1 rounded">
                          Enabled
                        </span>
                      ) : (
                        <span className="text-xs bg-red-900 text-red-300 px-2 py-1 rounded">
                          Disabled
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-400">
                <p>No commands found. Create your first command!</p>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default Commands;