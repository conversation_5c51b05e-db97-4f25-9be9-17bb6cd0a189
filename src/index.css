@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --twitch-purple: 145, 70, 255;
  --twitch-purple-dark: 100, 65, 165;
  --accent-blue: 28, 126, 214;
  --accent-green: 0, 184, 148;
  --accent-red: 242, 78, 78;
}

@layer base {
  body {
    @apply bg-gray-900 text-gray-100 min-h-screen;
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-bold;
  }
  
  h1 {
    @apply text-3xl;
  }
  
  h2 {
    @apply text-2xl;
  }
  
  h3 {
    @apply text-xl;
  }
  
  h4 {
    @apply text-lg;
  }
  
  ::selection {
    @apply bg-purple-700 text-white;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-all duration-200;
  }
  
  .btn-primary {
    @apply bg-purple-600 hover:bg-purple-700 text-white;
  }
  
  .btn-secondary {
    @apply bg-gray-700 hover:bg-gray-600 text-white;
  }
  
  .btn-danger {
    @apply bg-red-600 hover:bg-red-700 text-white;
  }
  
  .card {
    @apply bg-gray-800 rounded-lg p-6 shadow-lg border border-gray-700;
  }
  
  .input {
    @apply bg-gray-700 border border-gray-600 rounded-md px-4 py-2 text-white placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500;
  }
}

.animate-pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out forwards;
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500;
}