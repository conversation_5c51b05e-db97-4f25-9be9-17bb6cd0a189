import axios from 'axios';
import dotenv from 'dotenv';

dotenv.config();

export type Song = {
  id: string;
  title: string;
  artist: string;
  thumbnail: string;
  duration: number;
  requestedBy: string;
  source: string;
  sourceId: string;
};

export class YouTubeService {
  private apiKey: string | undefined;

  constructor() {
    this.apiKey = process.env.YOUTUBE_API_KEY;
  }

  public async searchSong(query: string, requestedBy: string): Promise<Song | null> {
    // Check if it's already a YouTube URL
    const youtubeUrlMatch = query.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/);
    
    if (youtubeUrlMatch) {
      const videoId = youtubeUrlMatch[1];
      return this.getVideoDetails(videoId, requestedBy);
    }

    // Search for the song
    return this.searchByQuery(query, requestedBy);
  }

  private async getVideoDetails(videoId: string, requestedBy: string): Promise<Song | null> {
    if (!this.apiKey) {
      console.warn('YouTube API key not configured, returning mock data');
      return this.createMockSong(videoId, requestedBy);
    }

    try {
      const response = await axios.get('https://www.googleapis.com/youtube/v3/videos', {
        params: {
          part: 'snippet,contentDetails',
          id: videoId,
          key: this.apiKey
        }
      });

      const video = response.data.items[0];
      if (!video) return null;

      return this.formatVideoToSong(video, requestedBy);
    } catch (error) {
      console.error('Error fetching video details:', error);
      return this.createMockSong(videoId, requestedBy);
    }
  }

  private async searchByQuery(query: string, requestedBy: string): Promise<Song | null> {
    if (!this.apiKey) {
      console.warn('YouTube API key not configured, returning mock data');
      return this.createMockSongFromQuery(query, requestedBy);
    }

    try {
      const response = await axios.get('https://www.googleapis.com/youtube/v3/search', {
        params: {
          part: 'snippet',
          q: query,
          type: 'video',
          maxResults: 1,
          key: this.apiKey
        }
      });

      const video = response.data.items[0];
      if (!video) return null;

      // Get additional details including duration
      return this.getVideoDetails(video.id.videoId, requestedBy);
    } catch (error) {
      console.error('Error searching YouTube:', error);
      return this.createMockSongFromQuery(query, requestedBy);
    }
  }

  private formatVideoToSong(video: any, requestedBy: string): Song {
    const duration = this.parseDuration(video.contentDetails.duration);
    
    return {
      id: this.generateId(),
      title: video.snippet.title,
      artist: video.snippet.channelTitle,
      thumbnail: video.snippet.thumbnails.medium?.url || video.snippet.thumbnails.default.url,
      duration,
      requestedBy,
      source: 'youtube',
      sourceId: video.id
    };
  }

  private parseDuration(duration: string): number {
    // Parse ISO 8601 duration format (PT4M13S)
    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
    if (!match) return 0;

    const hours = parseInt(match[1] || '0');
    const minutes = parseInt(match[2] || '0');
    const seconds = parseInt(match[3] || '0');

    return hours * 3600 + minutes * 60 + seconds;
  }

  private createMockSong(videoId: string, requestedBy: string): Song {
    return {
      id: this.generateId(),
      title: 'Unknown Song',
      artist: 'Unknown Artist',
      thumbnail: `https://i.ytimg.com/vi/${videoId}/hqdefault.jpg`,
      duration: 180, // 3 minutes default
      requestedBy,
      source: 'youtube',
      sourceId: videoId
    };
  }

  private createMockSongFromQuery(query: string, requestedBy: string): Song {
    return {
      id: this.generateId(),
      title: query,
      artist: 'Unknown Artist',
      thumbnail: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/hqdefault.jpg', // Rick Roll as default
      duration: 180, // 3 minutes default
      requestedBy,
      source: 'youtube',
      sourceId: 'dQw4w9WgXcQ' // Rick Roll as default
    };
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }
}
