import { nanoid } from 'nanoid';

type Song = {
  id: string;
  title: string;
  artist: string;
  thumbnail: string;
  duration: number;
  requestedBy: string;
  source: string;
  sourceId: string;
};

// Initial songs for the queue (demo data)
const initialQueue: Song[] = [
  {
    id: nanoid(),
    title: 'Never Gonna Give You Up',
    artist: '<PERSON>',
    thumbnail: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/hqdefault.jpg',
    duration: 213,
    requestedBy: 'ViewerName123',
    source: 'youtube',
    sourceId: 'dQw4w9WgXcQ'
  },
  {
    id: nanoid(),
    title: 'Take On Me',
    artist: 'a-ha',
    thumbnail: 'https://i.ytimg.com/vi/djV11Xbc914/hqdefault.jpg',
    duration: 225,
    requestedBy: 'ModeratorUser',
    source: 'youtube',
    sourceId: 'djV11Xbc914'
  },
  {
    id: nanoid(),
    title: '<PERSON>',
    artist: '<PERSON>',
    thumbnail: 'https://i.ytimg.com/vi/Zi_XLOBDo_Y/hqdefault.jpg',
    duration: 294,
    requestedBy: 'RandomViewer42',
    source: 'youtube',
    sourceId: 'Zi_XLOBDo_Y'
  }
];

export class SongRequestManager {
  private queue: Song[] = [];
  private currentSong: (Song & { currentTime: number }) | null = null;
  private playing: boolean = false;
  private songInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.queue = [...initialQueue];
    
    // For demo purposes, set a current song
    if (this.queue.length > 0) {
      const song = this.queue.shift()!;
      this.currentSong = { ...song, currentTime: 45 };
      this.playing = true;
      
      // Start the song progress timer for demo
      this.startSongProgress();
    }
  }

  private startSongProgress(): void {
    if (this.songInterval) {
      clearInterval(this.songInterval);
    }

    this.songInterval = setInterval(() => {
      if (!this.currentSong || !this.playing) return;

      this.currentSong.currentTime += 1;

      // Auto-skip when song ends
      if (this.currentSong.currentTime >= this.currentSong.duration) {
        this.skip();
      }
    }, 1000);
  }

  public getQueue(): Song[] {
    return this.queue;
  }

  public getCurrentSong(): (Song & { currentTime: number }) | null {
    return this.currentSong;
  }

  public isPlaying(): boolean {
    return this.playing;
  }

  public addSong(song: Song): void {
    this.queue.push({
      ...song,
      id: nanoid()
    });

    // If no song is playing, start this one
    if (!this.currentSong) {
      this.skip();
    }
  }

  public removeSong(id: string): void {
    this.queue = this.queue.filter(song => song.id !== id);
  }

  public play(): void {
    if (!this.currentSong && this.queue.length > 0) {
      const song = this.queue.shift()!;
      this.currentSong = { ...song, currentTime: 0 };
    }

    this.playing = true;
    this.startSongProgress();
  }

  public pause(): void {
    this.playing = false;
    
    if (this.songInterval) {
      clearInterval(this.songInterval);
      this.songInterval = null;
    }
  }

  public skip(): void {
    if (this.queue.length > 0) {
      const song = this.queue.shift()!;
      this.currentSong = { ...song, currentTime: 0 };
      this.playing = true;
      this.startSongProgress();
    } else {
      this.currentSong = null;
      this.playing = false;
      
      if (this.songInterval) {
        clearInterval(this.songInterval);
        this.songInterval = null;
      }
    }
  }

  public playSongById(id: string): void {
    const songIndex = this.queue.findIndex(song => song.id === id);
    
    if (songIndex !== -1) {
      const song = this.queue[songIndex];
      this.queue.splice(songIndex, 1);
      
      if (this.currentSong) {
        // Put current song back in queue at the front
        this.queue.unshift({
          ...this.currentSong,
          id: nanoid() // Generate new ID to avoid conflicts
        });
      }
      
      this.currentSong = { ...song, currentTime: 0 };
      this.playing = true;
      this.startSongProgress();
    }
  }

  public reorderQueue(sourceIndex: number, destinationIndex: number): void {
    if (sourceIndex === destinationIndex) return;
    
    const queue = [...this.queue];
    const [removed] = queue.splice(sourceIndex, 1);
    queue.splice(destinationIndex, 0, removed);
    
    this.queue = queue;
  }
}