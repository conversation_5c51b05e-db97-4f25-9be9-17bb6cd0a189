import dotenv from 'dotenv';
dotenv.config();

import express from 'express';
import http from 'http';
import cors from 'cors';
import { Server } from 'socket.io';
import routes from './routes';
import { TwitchBot } from './TwitchBot';
import { CommandManager } from './CommandManager';
import { GiveawayManager } from './GiveawayManager';
import { SongRequestManager } from './SongRequestManager';
import { YouTubeService } from './YouTubeService';

const app = express();
const server = http.createServer(app);
const io = new Server(server, {
  cors: {
    origin: '*',
    methods: ['GET', 'POST']
  }
});

app.use(cors());
app.use(express.json());
app.use(routes);

// Initialize managers
const commandManager = new CommandManager();
const giveawayManager = new GiveawayManager();
const songRequestManager = new SongRequestManager();
const youtubeService = new YouTubeService();
const twitchBot = TwitchBot.getInstance();

// Connect TwitchBot to SongRequestManager
twitchBot.setSongRequestCallback(async (username: string, songQuery: string) => {
  try {
    const song = await youtubeService.searchSong(songQuery, username);
    if (song) {
      songRequestManager.addSong(song);
      // Emit to all connected clients
      io.emit('queueUpdate', songRequestManager.getQueue());
      console.log(`Song request added: ${song.title} by ${song.artist} (requested by ${username})`);
    }
  } catch (error) {
    console.error('Error processing song request:', error);
  }
});

// Connect TwitchBot to real-time updates
twitchBot.setMetricsUpdateCallback((metrics) => {
  io.emit('metrics', metrics);
});

twitchBot.setEventsUpdateCallback((events) => {
  io.emit('recentEvents', events);
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('A user connected');

  // Command-related events
  socket.on('getCommands', () => {
    socket.emit('commandsUpdate', commandManager.getCommands());
  });

  socket.on('addCommand', (command) => {
    commandManager.addCommand(command);
    io.emit('commandsUpdate', commandManager.getCommands());
  });

  socket.on('updateCommand', (command) => {
    commandManager.updateCommand(command);
    io.emit('commandsUpdate', commandManager.getCommands());
  });

  socket.on('deleteCommand', (id) => {
    commandManager.deleteCommand(id);
    io.emit('commandsUpdate', commandManager.getCommands());
  });

  // Giveaway-related events
  socket.on('getGiveaways', () => {
    socket.emit('activeGiveawayUpdate', giveawayManager.getActiveGiveaway());
    socket.emit('pastGiveawaysUpdate', giveawayManager.getPastGiveaways());
  });

  socket.on('startGiveaway', (giveaway) => {
    giveawayManager.startGiveaway(giveaway);
    io.emit('activeGiveawayUpdate', giveawayManager.getActiveGiveaway());
  });

  socket.on('endGiveaway', ({ giveawayId, pastGiveaway }) => {
    giveawayManager.endGiveaway();
    giveawayManager.addPastGiveaway(pastGiveaway);
    io.emit('activeGiveawayUpdate', giveawayManager.getActiveGiveaway());
    io.emit('pastGiveawaysUpdate', giveawayManager.getPastGiveaways());
  });

  // Song request-related events
  socket.on('getSongRequests', () => {
    socket.emit('queueUpdate', songRequestManager.getQueue());
    socket.emit('currentSongUpdate', songRequestManager.getCurrentSong());
    socket.emit('playbackUpdate', songRequestManager.isPlaying());
  });

  socket.on('addSong', (song) => {
    songRequestManager.addSong(song);
    io.emit('queueUpdate', songRequestManager.getQueue());
  });

  socket.on('removeSong', (id) => {
    songRequestManager.removeSong(id);
    io.emit('queueUpdate', songRequestManager.getQueue());
  });

  socket.on('playSong', (id) => {
    if (id) {
      songRequestManager.playSongById(id);
    } else {
      songRequestManager.play();
    }
    io.emit('currentSongUpdate', songRequestManager.getCurrentSong());
    io.emit('playbackUpdate', songRequestManager.isPlaying());
    io.emit('queueUpdate', songRequestManager.getQueue());
  });

  socket.on('pauseSong', () => {
    songRequestManager.pause();
    io.emit('playbackUpdate', songRequestManager.isPlaying());
  });

  socket.on('skipSong', () => {
    songRequestManager.skip();
    io.emit('currentSongUpdate', songRequestManager.getCurrentSong());
    io.emit('playbackUpdate', songRequestManager.isPlaying());
    io.emit('queueUpdate', songRequestManager.getQueue());
  });

  socket.on('reorderQueue', ({ sourceIndex, destinationIndex }) => {
    songRequestManager.reorderQueue(sourceIndex, destinationIndex);
    io.emit('queueUpdate', songRequestManager.getQueue());
  });

  socket.on('searchSongs', async (term) => {
    try {
      // Search for multiple songs
      const searchPromises = [];
      for (let i = 0; i < 4; i++) {
        searchPromises.push(youtubeService.searchSong(`${term} ${i > 0 ? `song ${i}` : ''}`, 'System'));
      }

      const results = await Promise.all(searchPromises);
      const validResults = results.filter(result => result !== null);

      socket.emit('searchResults', validResults);
    } catch (error) {
      console.error('Error searching songs:', error);
      socket.emit('searchResults', []);
    }
  });

  // Chat-related events
  socket.on('getChatHistory', () => {
    socket.emit('chatHistory', twitchBot.getChatHistory());
  });

  socket.on('sendChatMessage', (message) => {
    const chatMessage = twitchBot.sendMessage(message);
    io.emit('chatMessage', chatMessage);
  });

  // Metrics and events
  socket.on('getMetrics', () => {
    socket.emit('metrics', twitchBot.getMetrics());
  });

  socket.on('getRecentEvents', () => {
    socket.emit('recentEvents', twitchBot.getRecentEvents());
  });

  socket.on('disconnect', () => {
    console.log('A user disconnected');
  });
});

// Start server
const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});