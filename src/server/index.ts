import dotenv from 'dotenv';
dotenv.config();

import express from 'express';
import http from 'http';
import cors from 'cors';
import { Server } from 'socket.io';
import routes from './routes';
import { TwitchBot } from './TwitchBot';
import { CommandManager } from './CommandManager';
import { GiveawayManager } from './GiveawayManager';
import { SongRequestManager } from './SongRequestManager';
import { YouTubeService } from './YouTubeService';

const app = express();
const server = http.createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env.NODE_ENV === 'production' ? false : ['http://localhost:5173', 'http://localhost:5174', 'http://localhost:3000'],
    methods: ['GET', 'POST'],
    credentials: true
  },
  allowEIO3: true
});

app.use(cors({
  origin: process.env.NODE_ENV === 'production' ? false : ['http://localhost:5173', 'http://localhost:5174', 'http://localhost:3000'],
  credentials: true
}));
app.use(express.json());

// Add a simple test endpoint
app.get('/test', (req, res) => {
  res.json({ message: 'Server is working!', timestamp: new Date().toISOString() });
});

app.use(routes);

// Initialize managers
const commandManager = new CommandManager();
const giveawayManager = new GiveawayManager();
const songRequestManager = new SongRequestManager();
const youtubeService = new YouTubeService();
const twitchBot = TwitchBot.getInstance();

// Connect TwitchBot to SongRequestManager
twitchBot.setSongRequestCallback(async (username: string, songQuery: string) => {
  try {
    const song = await youtubeService.searchSong(songQuery, username);
    if (song) {
      songRequestManager.addSong(song);
      // Emit to all connected clients
      io.emit('queueUpdate', songRequestManager.getQueue());
      console.log(`Song request added: ${song.title} by ${song.artist} (requested by ${username})`);
    }
  } catch (error) {
    console.error('Error processing song request:', error);
  }
});

// Connect TwitchBot to GiveawayManager
twitchBot.setGiveawayManager(giveawayManager);

// Connect GiveawayManager to TwitchBot for chat announcements
giveawayManager.setChatCallback((message: string) => {
  twitchBot.sendMessage(message);
});

// Connect TwitchBot to real-time updates
twitchBot.setMetricsUpdateCallback((metrics) => {
  io.emit('metrics', metrics);
});

twitchBot.setEventsUpdateCallback((events) => {
  io.emit('recentEvents', events);
});

twitchBot.setChatUpdateCallback((messages) => {
  io.emit('chatHistory', messages);
});

// Emit giveaway updates every 3 seconds
setInterval(() => {
  io.emit('giveawayUpdate', {
    active: giveawayManager.getActiveGiveaway(),
    past: giveawayManager.getPastGiveaways()
  });
}, 3000);

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('✅ Socket.IO: A user connected with ID:', socket.id);
  console.log('✅ Socket.IO: Client info:', {
    address: socket.handshake.address,
    headers: socket.handshake.headers.origin,
    transport: socket.conn.transport.name
  });

  // Command-related events (consolidated below)

  // Old giveaway handlers removed - using new consolidated ones below

  // Song request-related events
  socket.on('getSongRequests', () => {
    socket.emit('queueUpdate', songRequestManager.getQueue());
    socket.emit('currentSongUpdate', songRequestManager.getCurrentSong());
    socket.emit('playbackUpdate', songRequestManager.isPlaying());
  });

  socket.on('addSong', (song) => {
    const success = songRequestManager.addSong(song);
    if (success) {
      io.emit('queueUpdate', songRequestManager.getQueue());
      io.emit('songRequestSuccess', { message: `Added "${song.title}" to queue` });
    } else {
      socket.emit('songRequestError', {
        message: `Max songs per user (${songRequestManager.getMaxSongsPerUser()}) reached for ${song.requestedBy}`
      });
    }
  });

  socket.on('removeSong', (id) => {
    songRequestManager.removeSong(id);
    io.emit('queueUpdate', songRequestManager.getQueue());
  });

  socket.on('playSong', (id) => {
    if (id) {
      songRequestManager.playSongById(id);
    } else {
      songRequestManager.play();
    }
    io.emit('currentSongUpdate', songRequestManager.getCurrentSong());
    io.emit('playbackUpdate', songRequestManager.isPlaying());
    io.emit('queueUpdate', songRequestManager.getQueue());
  });

  socket.on('pauseSong', () => {
    songRequestManager.pause();
    io.emit('playbackUpdate', songRequestManager.isPlaying());
  });

  socket.on('skipSong', () => {
    songRequestManager.skip();
    io.emit('currentSongUpdate', songRequestManager.getCurrentSong());
    io.emit('playbackUpdate', songRequestManager.isPlaying());
    io.emit('queueUpdate', songRequestManager.getQueue());
  });

  socket.on('reorderQueue', ({ sourceIndex, destinationIndex }) => {
    songRequestManager.reorderQueue(sourceIndex, destinationIndex);
    io.emit('queueUpdate', songRequestManager.getQueue());
  });

  socket.on('searchSongs', async (term) => {
    try {
      console.log(`🔍 Searching for: ${term}`);
      const results = await songRequestManager.searchSongs(term);
      console.log(`✅ Found ${results.length} results`);
      socket.emit('searchResults', results);
    } catch (error) {
      console.error('Error searching songs:', error);
      socket.emit('searchResults', []);
    }
  });

  // Chat-related events
  socket.on('getChatHistory', () => {
    socket.emit('chatHistory', twitchBot.getChatHistory());
  });

  socket.on('sendChatMessage', (message) => {
    const chatMessage = twitchBot.sendMessage(message);
    io.emit('chatMessage', chatMessage);
  });

  // Metrics and events
  socket.on('getMetrics', () => {
    socket.emit('metrics', twitchBot.getMetrics());
  });

  socket.on('getRecentEvents', () => {
    socket.emit('recentEvents', twitchBot.getRecentEvents());
  });

  // Giveaway events
  socket.on('getGiveaways', () => {
    socket.emit('giveawayUpdate', {
      active: giveawayManager.getActiveGiveaway(),
      past: giveawayManager.getPastGiveaways()
    });
  });

  socket.on('startGiveaway', (giveawayData) => {
    giveawayManager.startGiveaway(giveawayData);
    io.emit('giveawayUpdate', {
      active: giveawayManager.getActiveGiveaway(),
      past: giveawayManager.getPastGiveaways()
    });
  });

  socket.on('endGiveaway', () => {
    giveawayManager.endGiveaway();
    io.emit('giveawayUpdate', {
      active: giveawayManager.getActiveGiveaway(),
      past: giveawayManager.getPastGiveaways()
    });
  });

  socket.on('resetGiveaway', () => {
    giveawayManager.resetGiveaway();
    io.emit('giveawayUpdate', {
      active: giveawayManager.getActiveGiveaway(),
      past: giveawayManager.getPastGiveaways()
    });
  });

  socket.on('drawWinner', () => {
    console.log('🎲 Received drawWinner request from client');
    const winner = giveawayManager.drawWinner();
    if (winner) {
      console.log(`🏆 Winner drawn: ${winner.username}`);
      io.emit('winnerDrawn', winner);
      io.emit('giveawayUpdate', {
        active: giveawayManager.getActiveGiveaway(),
        past: giveawayManager.getPastGiveaways()
      });
      console.log('📡 Emitted winnerDrawn and giveawayUpdate events');
    } else {
      console.log('❌ No winner could be drawn');
    }
  });

  socket.on('getUserProfile', (username) => {
    const profile = giveawayManager.getUserProfile(username);
    socket.emit('userProfile', profile);
  });

  socket.on('addTestEntry', (username) => {
    console.log(`🧪 Adding test entry: ${username}`);
    giveawayManager.addEntry({
      username,
      timestamp: new Date().toLocaleTimeString()
    });
    io.emit('giveawayUpdate', {
      active: giveawayManager.getActiveGiveaway(),
      past: giveawayManager.getPastGiveaways()
    });
  });

  // Commands events
  socket.on('getCommands', () => {
    socket.emit('commandsUpdate', commandManager.getCommands());
  });

  socket.on('addCommand', (command) => {
    commandManager.addCommand(command);
    io.emit('commandsUpdate', commandManager.getCommands());
  });

  socket.on('updateCommand', (command) => {
    commandManager.updateCommand(command);
    io.emit('commandsUpdate', commandManager.getCommands());
  });

  socket.on('deleteCommand', (id) => {
    commandManager.deleteCommand(id);
    io.emit('commandsUpdate', commandManager.getCommands());
  });

  socket.on('disconnect', (reason) => {
    console.log('❌ Socket.IO: User disconnected:', socket.id, 'Reason:', reason);
  });
});

// Start server
const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});