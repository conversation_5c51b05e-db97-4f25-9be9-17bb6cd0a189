import { nanoid } from 'nanoid';
import { getRandomColor } from '../lib/utils';
import tmi from 'tmi.js';
import dotenv from 'dotenv';

dotenv.config();

type ChatMessage = {
  id: string;
  username: string;
  message: string;
  color: string;
  timestamp: number;
  isMod: boolean;
  isSubscriber: boolean;
};

type CommandHandler = (channel: string, userstate: tmi.ChatUserstate, message: string, self: boolean) => void;

export class TwitchBot {
  private client: tmi.Client | null = null;
  private chatHistory: ChatMessage[] = [];
  private metrics = {
    chatMessages: 0,
    viewers: 0,
    commandsUsed: 0,
    uptime: '00:00:00'
  };
  private recentEvents: string[] = [];
  private connected = false;
  private startTime = Date.now();
  private commandHandlers = new Map<string, CommandHandler>();
  private songRequestCallback: ((username: string, song: string) => void) | null = null;

  constructor() {
    this.initializeTwitchClient();
    this.setupCommandHandlers();
    this.startUptimeTimer();

    // Initialize with some mock data for demo purposes
    this.generateMockChatHistory();
  }

  private initializeTwitchClient() {
    const username = process.env.TWITCH_USERNAME;
    const token = process.env.TWITCH_OAUTH_TOKEN;
    const channel = process.env.TWITCH_CHANNEL;

    if (!username || !token || !channel) {
      console.warn('Twitch credentials not found in environment variables. Running in demo mode.');
      return;
    }

    this.client = new tmi.Client({
      options: { debug: true },
      connection: {
        reconnect: true,
        secure: true
      },
      identity: {
        username: username,
        password: token
      },
      channels: [channel]
    });

    this.setupEventHandlers();
    this.connectToTwitch();
  }

  private setupEventHandlers() {
    if (!this.client) return;

    this.client.on('connected', (addr, port) => {
      console.log(`Connected to Twitch chat at ${addr}:${port}`);
      this.connected = true;
      this.addRecentEvent('Bot connected to Twitch chat');
    });

    this.client.on('disconnected', (reason) => {
      console.log(`Disconnected from Twitch: ${reason}`);
      this.connected = false;
      this.addRecentEvent('Bot disconnected from Twitch chat');
    });

    this.client.on('message', (channel, userstate, message, self) => {
      if (self) return;

      this.handleChatMessage(channel, userstate, message);
      this.handleCommands(channel, userstate, message, self);
    });

    this.client.on('subscription', (_channel, username, method, _message, _userstate) => {
      this.addRecentEvent(`${username} subscribed (${method.plan}) - just now`);
    });

    this.client.on('cheer', (_channel, userstate, _message) => {
      const bits = userstate.bits;
      this.addRecentEvent(`${userstate.username} cheered ${bits} bits - just now`);
    });
  }

  private async connectToTwitch() {
    if (!this.client) return;

    try {
      await this.client.connect();
    } catch (error) {
      console.error('Failed to connect to Twitch:', error);
      this.addRecentEvent('Failed to connect to Twitch chat');
    }
  }

  private setupCommandHandlers() {
    // Song request command
    this.commandHandlers.set('sr', this.handleSongRequest.bind(this));
    this.commandHandlers.set('songrequest', this.handleSongRequest.bind(this));

    // Basic commands
    this.commandHandlers.set('uptime', this.handleUptimeCommand.bind(this));
    this.commandHandlers.set('queue', this.handleQueueCommand.bind(this));
  }

  private startUptimeTimer() {
    setInterval(() => {
      const elapsed = Date.now() - this.startTime;
      const hours = Math.floor(elapsed / (1000 * 60 * 60));
      const minutes = Math.floor((elapsed % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((elapsed % (1000 * 60)) / 1000);

      this.metrics.uptime = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
    }, 1000);
  }

  private handleChatMessage(_channel: string, userstate: tmi.ChatUserstate, message: string) {
    const chatMessage: ChatMessage = {
      id: nanoid(),
      username: userstate.username || 'Unknown',
      message,
      color: userstate.color || getRandomColor(),
      timestamp: Date.now(),
      isMod: userstate.mod || false,
      isSubscriber: userstate.subscriber || false
    };

    this.chatHistory.push(chatMessage);
    this.metrics.chatMessages++;

    // Keep only last 100 messages
    if (this.chatHistory.length > 100) {
      this.chatHistory = this.chatHistory.slice(-100);
    }
  }

  private handleCommands(channel: string, userstate: tmi.ChatUserstate, message: string, self: boolean) {
    if (self || !message.startsWith('!')) return;

    const args = message.slice(1).split(' ');
    const command = args[0].toLowerCase();

    const handler = this.commandHandlers.get(command);
    if (handler) {
      handler(channel, userstate, message, self);
      this.metrics.commandsUsed++;
    }
  }

  private addRecentEvent(event: string) {
    this.recentEvents.unshift(event);
    if (this.recentEvents.length > 10) {
      this.recentEvents = this.recentEvents.slice(0, 10);
    }
  }

  private handleSongRequest(channel: string, userstate: tmi.ChatUserstate, message: string, _self: boolean) {
    const args = message.slice(1).split(' ');
    const songQuery = args.slice(1).join(' ');

    if (!songQuery) {
      this.client?.say(channel, `@${userstate.username} Please provide a song to request! Usage: !sr <song name or URL>`);
      return;
    }

    const username = userstate.username || 'Unknown';

    // Call the song request callback if it's set
    if (this.songRequestCallback) {
      this.songRequestCallback(username, songQuery);
      this.client?.say(channel, `@${username} Your song request has been added to the queue!`);
    } else {
      this.client?.say(channel, `@${username} Song requests are currently disabled.`);
    }
  }

  private handleUptimeCommand(channel: string, userstate: tmi.ChatUserstate, _message: string, _self: boolean) {
    this.client?.say(channel, `@${userstate.username} Stream has been live for ${this.metrics.uptime}`);
  }

  private handleQueueCommand(channel: string, userstate: tmi.ChatUserstate, _message: string, _self: boolean) {
    // This would show current queue info - for now just a placeholder
    this.client?.say(channel, `@${userstate.username} Check the song queue on the stream overlay!`);
  }

  // Public method to set song request callback
  public setSongRequestCallback(callback: (username: string, song: string) => void) {
    this.songRequestCallback = callback;
  }

  // Public method to check connection status
  public isConnected(): boolean {
    return this.connected;
  }

  private generateMockChatHistory() {
    const users = [
      { name: 'ViewerOne', isMod: false, isSubscriber: false, color: getRandomColor() },
      { name: 'ModUser', isMod: true, isSubscriber: true, color: getRandomColor() },
      { name: 'Subscriber123', isMod: false, isSubscriber: true, color: getRandomColor() },
      { name: 'RegularViewer', isMod: false, isSubscriber: false, color: getRandomColor() },
      { name: 'StreamerName', isMod: true, isSubscriber: true, color: '#9146FF' }
    ];

    const messages = [
      'Hello everyone!',
      'Great stream today!',
      'How long have you been streaming?',
      'What game is this?',
      '!uptime',
      'LOL that was funny',
      'GG',
      'Can we play together sometime?',
      '!discord',
      'First time here, loving the content!'
    ];

    // Generate 20 random messages
    for (let i = 0; i < 20; i++) {
      const user = users[Math.floor(Math.random() * users.length)];
      const message = messages[Math.floor(Math.random() * messages.length)];
      const timestamp = Date.now() - Math.floor(Math.random() * 300000); // Random time in last 5 minutes

      this.chatHistory.push({
        id: nanoid(),
        username: user.name,
        message,
        color: user.color,
        timestamp,
        isMod: user.isMod,
        isSubscriber: user.isSubscriber
      });
    }

    // Sort by timestamp
    this.chatHistory.sort((a, b) => a.timestamp - b.timestamp);
  }

  public sendMessage(message: string) {
    const channel = process.env.TWITCH_CHANNEL;

    // Send to Twitch if connected
    if (this.client && this.connected && channel) {
      this.client.say(channel, message);
    }

    // Also add to local chat history for UI
    const chatMessage: ChatMessage = {
      id: nanoid(),
      username: process.env.TWITCH_USERNAME || 'StreamerName',
      message,
      color: '#9146FF',
      timestamp: Date.now(),
      isMod: true,
      isSubscriber: true
    };

    this.chatHistory.push(chatMessage);
    this.metrics.chatMessages++;

    return chatMessage;
  }

  public getChatHistory() {
    return this.chatHistory;
  }

  public getMetrics() {
    return this.metrics;
  }

  public getRecentEvents() {
    return this.recentEvents;
  }
}