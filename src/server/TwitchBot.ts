import { nanoid } from 'nanoid';
import { getRandomColor } from '../lib/utils';

// This is a mock class for demo purposes
// In a real app, this would use tmi.js to connect to Twitch chat
export class TwitchBot {
  private chatHistory: any[] = [];
  private metrics = {
    chatMessages: 123,
    viewers: 42,
    commandsUsed: 15,
    uptime: '01:23:45'
  };
  private recentEvents = [
    'UserXYZ subscribed (Tier 1) - 2 minutes ago',
    'NewViewer123 followed the channel - 5 minutes ago',
    'ModUser banned SpamBot123 - 10 minutes ago',
    'StreamerFriend raided with 15 viewers - 20 minutes ago'
  ];

  constructor() {
    // Initialize mock chat history
    this.generateMockChatHistory();
    
    // Update uptime periodically for demo
    setInterval(() => {
      const [hours, minutes, seconds] = this.metrics.uptime.split(':').map(Number);
      let newSeconds = seconds + 1;
      let newMinutes = minutes;
      let newHours = hours;
      
      if (newSeconds >= 60) {
        newSeconds = 0;
        newMinutes++;
      }
      
      if (newMinutes >= 60) {
        newMinutes = 0;
        newHours++;
      }
      
      this.metrics.uptime = `${String(newHours).padStart(2, '0')}:${String(newMinutes).padStart(2, '0')}:${String(newSeconds).padStart(2, '0')}`;
    }, 1000);
  }

  private generateMockChatHistory() {
    const users = [
      { name: 'ViewerOne', isMod: false, isSubscriber: false, color: getRandomColor() },
      { name: 'ModUser', isMod: true, isSubscriber: true, color: getRandomColor() },
      { name: 'Subscriber123', isMod: false, isSubscriber: true, color: getRandomColor() },
      { name: 'RegularViewer', isMod: false, isSubscriber: false, color: getRandomColor() },
      { name: 'StreamerName', isMod: true, isSubscriber: true, color: '#9146FF' }
    ];
    
    const messages = [
      'Hello everyone!',
      'Great stream today!',
      'How long have you been streaming?',
      'What game is this?',
      '!uptime',
      'LOL that was funny',
      'GG',
      'Can we play together sometime?',
      '!discord',
      'First time here, loving the content!'
    ];
    
    // Generate 20 random messages
    for (let i = 0; i < 20; i++) {
      const user = users[Math.floor(Math.random() * users.length)];
      const message = messages[Math.floor(Math.random() * messages.length)];
      const timestamp = Date.now() - Math.floor(Math.random() * 300000); // Random time in last 5 minutes
      
      this.chatHistory.push({
        id: nanoid(),
        username: user.name,
        message,
        color: user.color,
        timestamp,
        isMod: user.isMod,
        isSubscriber: user.isSubscriber
      });
    }
    
    // Sort by timestamp
    this.chatHistory.sort((a, b) => a.timestamp - b.timestamp);
  }

  public sendMessage(message: string) {
    const chatMessage = {
      id: nanoid(),
      username: 'StreamerName',
      message,
      color: '#9146FF',
      timestamp: Date.now(),
      isMod: true,
      isSubscriber: true
    };
    
    this.chatHistory.push(chatMessage);
    this.metrics.chatMessages++;
    
    return chatMessage;
  }

  public getChatHistory() {
    return this.chatHistory;
  }

  public getMetrics() {
    return this.metrics;
  }

  public getRecentEvents() {
    return this.recentEvents;
  }
}