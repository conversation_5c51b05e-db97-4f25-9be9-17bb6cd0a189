import express from 'express';
import { TwitchBot } from '../TwitchBot';

const router = express.Router();

router.get('/status', (req, res) => {
  const twitchBot = TwitchBot.getInstance();
  res.json({ connected: twitchBot.isConnected() });
});

router.get('/metrics', (req, res) => {
  const twitchBot = TwitchBot.getInstance();
  const metrics = twitchBot.getMetrics();
  res.json(metrics);
});

export default router;