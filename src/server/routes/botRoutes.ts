import express from 'express';
import { TwitchBot } from '../TwitchBot';

const router = express.Router();

// We'll need to get the bot instance from the main server
// For now, create a new instance (this should be refactored to use a singleton)
const twitchBot = new TwitchBot();

router.get('/status', (req, res) => {
  res.json({ connected: twitchBot.isConnected() });
});

router.get('/metrics', (req, res) => {
  const metrics = twitchBot.getMetrics();
  res.json(metrics);
});

export default router;