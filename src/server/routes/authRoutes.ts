import express from 'express';

const router = express.Router();

router.post('/login', (req, res) => {
  const { username, password } = req.body;
  // In a real app, validate credentials and generate JWT
  res.json({
    token: 'mock_jwt_token',
    user: {
      id: '123',
      username,
      avatar: `https://ui-avatars.com/api/?name=${username}&background=random`
    }
  });
});

router.post('/logout', (req, res) => {
  res.json({ success: true });
});

export default router;