import express from 'express';
import { GiveawayManager } from '../GiveawayManager';

const router = express.Router();
const giveawayManager = new GiveawayManager();

router.get('/', (req, res) => {
  const activeGiveaway = giveawayManager.getActiveGiveaway();
  const pastGiveaways = giveawayManager.getPastGiveaways();
  res.json({ active: activeGiveaway, past: pastGiveaways });
});

router.post('/', (req, res) => {
  const giveaway = req.body;
  giveawayManager.startGiveaway(giveaway);
  res.json(giveawayManager.getActiveGiveaway());
});

router.post('/:id/end', (req, res) => {
  const { id } = req.params;
  giveawayManager.endGiveaway();
  res.json({ success: true });
});

router.post('/:id/draw', (req, res) => {
  const { id } = req.params;
  const winner = {
    username: 'RandomUser123', // In real app, this would be randomly selected
    timestamp: new Date().toISOString()
  };
  giveawayManager.addWinner(winner);
  res.json(winner);
});

export default router;