import express from 'express';
import { CommandManager } from '../CommandManager';

const router = express.Router();
const commandManager = new CommandManager();

router.get('/', (req, res) => {
  const commands = commandManager.getCommands();
  res.json(commands);
});

router.post('/', (req, res) => {
  const command = req.body;
  commandManager.addCommand(command);
  res.json(command);
});

router.put('/:id', (req, res) => {
  const command = req.body;
  commandManager.updateCommand(command);
  res.json(command);
});

router.delete('/:id', (req, res) => {
  const { id } = req.params;
  commandManager.deleteCommand(id);
  res.json({ success: true });
});

export default router;