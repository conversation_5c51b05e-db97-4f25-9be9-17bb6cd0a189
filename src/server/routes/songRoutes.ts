import express from 'express';
import { SongRequestManager } from '../SongRequestManager';

const router = express.Router();
const songManager = new SongRequestManager();

router.get('/', (req, res) => {
  const queue = songManager.getQueue();
  const currentSong = songManager.getCurrentSong();
  res.json({ queue, currentSong });
});

router.post('/', (req, res) => {
  const song = req.body;
  songManager.addSong(song);
  res.json(song);
});

router.delete('/:id', (req, res) => {
  const { id } = req.params;
  songManager.removeSong(id);
  res.json({ success: true });
});

router.put('/queue', (req, res) => {
  const { queue } = req.body;
  // Implement queue reordering logic
  res.json({ success: true });
});

export default router;