import express from 'express';
import commandRoutes from './commandRoutes';
import giveawayRoutes from './giveawayRoutes';
import songRoutes from './songRoutes';
import chatRoutes from './chatRoutes';
import botRoutes from './botRoutes';
import authRoutes from './authRoutes';

const router = express.Router();

router.use('/api/commands', commandRoutes);
router.use('/api/giveaways', giveawayRoutes);
router.use('/api/songs', songRoutes);
router.use('/api/chat', chatRoutes);
router.use('/api/bot', botRoutes);
router.use('/api/auth', authRoutes);

export default router;