import express from 'express';
import { TwitchBot } from '../TwitchBot';

const router = express.Router();
const twitchBot = new TwitchBot();

router.get('/history', (req, res) => {
  const history = twitchBot.getChatHistory();
  res.json(history);
});

router.post('/message', (req, res) => {
  const { message } = req.body;
  const chatMessage = twitchBot.sendMessage(message);
  res.json(chatMessage);
});

export default router;