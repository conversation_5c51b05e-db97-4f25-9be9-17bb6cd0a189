import express from 'express';
import { TwitchBot } from '../TwitchBot';

const router = express.Router();

router.get('/history', (req, res) => {
  const twitchBot = TwitchBot.getInstance();
  const history = twitchBot.getChatHistory();
  res.json(history);
});

router.post('/message', (req, res) => {
  const twitchBot = TwitchBot.getInstance();
  const { message } = req.body;
  const chatMessage = twitchBot.sendMessage(message);
  res.json(chatMessage);
});

export default router;