import { nanoid } from 'nanoid';
import { Database } from './Database';

export type Command = {
  id: string;
  name: string;
  response: string;
  userLevel: string;
  cooldown: number;
  enabled: boolean;
  usageCount: number;
};

export class CommandManager {
  private db: Database;
  private cooldowns = new Map<string, number>();

  constructor() {
    this.db = Database.getInstance();
  }

  public getCommands(): Command[] {
    return this.db.getCommands();
  }

  public getCommand(name: string): Command | undefined {
    return this.db.getCommands().find(cmd => cmd.name === name);
  }

  public addCommand(command: Command): void {
    const newCommand = {
      ...command,
      id: command.id || nanoid(),
      usageCount: command.usageCount || 0
    };
    this.db.addCommand(newCommand);
  }

  public updateCommand(command: Command): void {
    this.db.updateCommand(command);
  }

  public deleteCommand(id: string): void {
    this.db.deleteCommand(id);
  }

  public incrementUsage(name: string): void {
    this.db.incrementCommandUsage(name);
  }

  // Check if user can use command and handle cooldowns
  public canUseCommand(commandName: string, username: string, userLevel: string): { canUse: boolean; reason?: string } {
    const command = this.getCommand(commandName);

    if (!command) {
      return { canUse: false, reason: 'Command not found' };
    }

    if (!command.enabled) {
      return { canUse: false, reason: 'Command is disabled' };
    }

    // Check user level permissions
    if (!this.hasPermission(userLevel, command.userLevel)) {
      return { canUse: false, reason: 'Insufficient permissions' };
    }

    // Check cooldown
    const cooldownKey = `${commandName}:${username}`;
    const lastUsed = this.cooldowns.get(cooldownKey) || 0;
    const now = Date.now();
    const cooldownMs = command.cooldown * 1000;

    if (now - lastUsed < cooldownMs) {
      const remainingSeconds = Math.ceil((cooldownMs - (now - lastUsed)) / 1000);
      return { canUse: false, reason: `Command on cooldown for ${remainingSeconds}s` };
    }

    return { canUse: true };
  }

  public useCommand(commandName: string, username: string, userLevel: string): { success: boolean; response?: string; reason?: string } {
    const canUse = this.canUseCommand(commandName, username, userLevel);

    if (!canUse.canUse) {
      return { success: false, reason: canUse.reason };
    }

    const command = this.getCommand(commandName);
    if (!command) {
      return { success: false, reason: 'Command not found' };
    }

    // Set cooldown
    const cooldownKey = `${commandName}:${username}`;
    this.cooldowns.set(cooldownKey, Date.now());

    // Increment usage
    this.incrementUsage(commandName);

    // Process response with variables
    const response = this.processCommandResponse(command.response, username);

    return { success: true, response };
  }

  private hasPermission(userLevel: string, requiredLevel: string): boolean {
    const levels = ['everyone', 'subscriber', 'moderator', 'broadcaster'];
    const userLevelIndex = levels.indexOf(userLevel);
    const requiredLevelIndex = levels.indexOf(requiredLevel);

    return userLevelIndex >= requiredLevelIndex;
  }

  private processCommandResponse(response: string, username: string): string {
    const db = Database.getInstance();
    const metrics = db.getMetrics();

    return response
      .replace(/{user}/g, username)
      .replace(/{uptime}/g, metrics.uptime)
      .replace(/{chatMessages}/g, metrics.chatMessages.toString())
      .replace(/{commands}/g, metrics.commandsUsed.toString());
  }
}