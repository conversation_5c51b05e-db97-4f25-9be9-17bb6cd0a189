import { nanoid } from 'nanoid';

type Command = {
  id: string;
  name: string;
  response: string;
  userLevel: string;
  cooldown: number;
  enabled: boolean;
  usageCount: number;
};

// Initial commands for demo purposes
const initialCommands: Command[] = [
  {
    id: nanoid(),
    name: 'discord',
    response: 'Join our Discord server at: https://discord.gg/example',
    userLevel: 'everyone',
    cooldown: 10,
    enabled: true,
    usageCount: 42
  },
  {
    id: nanoid(),
    name: 'uptime',
    response: 'Stream has been live for {uptime}',
    userLevel: 'everyone',
    cooldown: 5,
    enabled: true,
    usageCount: 23
  },
  {
    id: nanoid(),
    name: 'socials',
    response: 'Follow me on Twitter: @twitchstreamer | Instagram: @streamer',
    userLevel: 'everyone',
    cooldown: 15,
    enabled: true,
    usageCount: 18
  },
  {
    id: nanoid(),
    name: 'lurk',
    response: '{user} is now lurking! Enjoy your lurk!',
    userLevel: 'everyone',
    cooldown: 0,
    enabled: true,
    usageCount: 12
  },
  {
    id: nanoid(),
    name: 'clear',
    response: 'Chat has been cleared by a moderator.',
    userLevel: 'moderator',
    cooldown: 0,
    enabled: true,
    usageCount: 5
  }
];

export class CommandManager {
  private commands: Command[] = [];

  constructor() {
    this.commands = [...initialCommands];
  }

  public getCommands(): Command[] {
    return this.commands;
  }

  public getCommand(name: string): Command | undefined {
    return this.commands.find(cmd => cmd.name === name);
  }

  public addCommand(command: Command): void {
    this.commands.push({
      ...command,
      id: command.id || nanoid(),
      usageCount: command.usageCount || 0
    });
  }

  public updateCommand(command: Command): void {
    const index = this.commands.findIndex(cmd => cmd.id === command.id);
    if (index !== -1) {
      this.commands[index] = command;
    }
  }

  public deleteCommand(id: string): void {
    this.commands = this.commands.filter(cmd => cmd.id !== id);
  }

  public incrementUsage(name: string): void {
    const command = this.commands.find(cmd => cmd.name === name);
    if (command) {
      command.usageCount++;
    }
  }
}