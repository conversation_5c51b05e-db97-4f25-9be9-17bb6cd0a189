import { nanoid } from 'nanoid';

type GiveawayEntry = {
  username: string;
  timestamp: string;
};

type GiveawayWinner = {
  username: string;
  timestamp: string;
};

type ActiveGiveaway = {
  id: string;
  title: string;
  prize: string;
  keyword: string;
  eligibility: string;
  startTime: string;
  endTime: string;
  timeRemaining: string;
  isPaused: boolean;
  entries: GiveawayEntry[];
  winners: GiveawayWinner[];
};

type PastGiveaway = {
  id: string;
  title: string;
  prize: string;
  date: string;
  entriesCount: number;
  winner: string;
};

// Initial past giveaways for demo
const initialPastGiveaways: PastGiveaway[] = [
  {
    id: nanoid(),
    title: '1000 Followers Celebration',
    prize: 'Steam Gift Card ($20)',
    date: '2023-06-15',
    entriesCount: 145,
    winner: 'ViewerName123'
  },
  {
    id: nanoid(),
    title: 'Monthly Subscriber Giveaway',
    prize: 'Gaming Headset',
    date: '2023-05-28',
    entriesCount: 87,
    winner: 'SubUser456'
  },
  {
    id: nanoid(),
    title: 'Game Launch Party',
    prize: 'Game Key',
    date: '2023-05-10',
    entriesCount: 212,
    winner: 'GamerFan789'
  }
];

export class GiveawayManager {
  private activeGiveaway: ActiveGiveaway | null = null;
  private pastGiveaways: PastGiveaway[] = [];
  private timerInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.pastGiveaways = [...initialPastGiveaways];
  }

  public getActiveGiveaway(): ActiveGiveaway | null {
    return this.activeGiveaway;
  }

  public getPastGiveaways(): PastGiveaway[] {
    return this.pastGiveaways;
  }

  public startGiveaway(giveaway: Partial<ActiveGiveaway>): void {
    if (this.activeGiveaway) {
      this.endGiveaway();
    }

    const durationMinutes = 5; // Default to 5 minutes
    const endTime = new Date(Date.now() + durationMinutes * 60 * 1000).toISOString();

    this.activeGiveaway = {
      id: nanoid(),
      title: giveaway.title || 'Giveaway',
      prize: giveaway.prize || 'Prize',
      keyword: giveaway.keyword || '!join',
      eligibility: giveaway.eligibility || 'everyone',
      startTime: new Date().toISOString(),
      endTime,
      timeRemaining: `${durationMinutes}:00`,
      isPaused: false,
      entries: [
        { username: 'Viewer1', timestamp: '2 minutes ago' },
        { username: 'Subscriber123', timestamp: '1 minute ago' },
        { username: 'Follower456', timestamp: 'Just now' },
      ],
      winners: []
    };

    // Start countdown timer for demo
    this.startTimer(durationMinutes * 60);
  }

  private startTimer(seconds: number): void {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
    }

    let remainingSeconds = seconds;

    this.timerInterval = setInterval(() => {
      if (!this.activeGiveaway || this.activeGiveaway.isPaused) return;

      remainingSeconds--;

      if (remainingSeconds <= 0) {
        clearInterval(this.timerInterval!);
        this.timerInterval = null;
        
        // Auto-end the giveaway when timer expires
        // In a real app, you might want to handle this differently
        // this.endGiveaway();
        
        if (this.activeGiveaway) {
          this.activeGiveaway.timeRemaining = '0:00';
        }
        
        return;
      }

      const minutes = Math.floor(remainingSeconds / 60);
      const secs = remainingSeconds % 60;

      if (this.activeGiveaway) {
        this.activeGiveaway.timeRemaining = `${minutes}:${secs.toString().padStart(2, '0')}`;
      }
    }, 1000);
  }

  public endGiveaway(): void {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
      this.timerInterval = null;
    }

    this.activeGiveaway = null;
  }

  public addPastGiveaway(giveaway: PastGiveaway): void {
    this.pastGiveaways.unshift(giveaway);
  }

  public addEntry(entry: GiveawayEntry): void {
    if (this.activeGiveaway) {
      this.activeGiveaway.entries.push(entry);
    }
  }

  public addWinner(winner: GiveawayWinner): void {
    if (this.activeGiveaway) {
      this.activeGiveaway.winners.push(winner);
    }
  }

  public resetGiveaway(): void {
    if (this.activeGiveaway) {
      this.activeGiveaway.entries = [];
      this.activeGiveaway.winners = [];
    }
  }
}