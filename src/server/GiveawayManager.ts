import { nanoid } from 'nanoid';
import { Database } from './Database';

type GiveawayEntry = {
  username: string;
  timestamp: string;
};

type GiveawayWinner = {
  username: string;
  timestamp: string;
};

type ActiveGiveaway = {
  id: string;
  title: string;
  prize: string;
  keyword: string;
  eligibility: string;
  startTime: string;
  endTime: string;
  timeRemaining: string;
  isPaused: boolean;
  entries: GiveawayEntry[];
  winners: GiveawayWinner[];
};

type PastGiveaway = {
  id: string;
  title: string;
  prize: string;
  date: string;
  entriesCount: number;
  winner: string;
};



export class GiveawayManager {
  private db: Database;
  private timerInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.db = Database.getInstance();
  }

  public getActiveGiveaway(): ActiveGiveaway | null {
    return this.db.getActiveGiveaway();
  }

  public getPastGiveaways(): PastGiveaway[] {
    return this.db.getPastGiveaways();
  }

  public startGiveaway(giveaway: Partial<ActiveGiveaway>): void {
    const activeGiveaway = this.db.getActiveGiveaway();
    if (activeGiveaway) {
      this.endGiveaway();
    }

    const durationMinutes = 5; // Default to 5 minutes
    const endTime = new Date(Date.now() + durationMinutes * 60 * 1000).toISOString();

    const newGiveaway = {
      id: nanoid(),
      title: giveaway.title || 'Giveaway',
      prize: giveaway.prize || 'Prize',
      keyword: giveaway.keyword || '!join',
      eligibility: giveaway.eligibility || 'everyone',
      startTime: new Date().toISOString(),
      endTime,
      timeRemaining: `${durationMinutes}:00`,
      isPaused: false,
      entries: [],
      winners: []
    };

    this.db.setActiveGiveaway(newGiveaway);

    // Start countdown timer
    this.startTimer(durationMinutes * 60);
  }

  private startTimer(seconds: number): void {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
    }

    let remainingSeconds = seconds;

    this.timerInterval = setInterval(() => {
      const activeGiveaway = this.db.getActiveGiveaway();
      if (!activeGiveaway || activeGiveaway.isPaused) return;

      remainingSeconds--;

      if (remainingSeconds <= 0) {
        clearInterval(this.timerInterval!);
        this.timerInterval = null;

        // Auto-end the giveaway when timer expires
        this.endGiveaway();
        return;
      }

      const minutes = Math.floor(remainingSeconds / 60);
      const secs = remainingSeconds % 60;

      // Update the giveaway in database
      const updatedGiveaway = {
        ...activeGiveaway,
        timeRemaining: `${minutes}:${secs.toString().padStart(2, '0')}`
      };
      this.db.setActiveGiveaway(updatedGiveaway);
    }, 1000);
  }

  public endGiveaway(): void {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
      this.timerInterval = null;
    }

    this.db.endActiveGiveaway();
  }

  public addEntry(entry: GiveawayEntry): void {
    const activeGiveaway = this.db.getActiveGiveaway();
    if (activeGiveaway) {
      const updatedGiveaway = {
        ...activeGiveaway,
        entries: [...activeGiveaway.entries, entry]
      };
      this.db.setActiveGiveaway(updatedGiveaway);
    }
  }

  public addWinner(winner: GiveawayWinner): void {
    const activeGiveaway = this.db.getActiveGiveaway();
    if (activeGiveaway) {
      const updatedGiveaway = {
        ...activeGiveaway,
        winners: [...activeGiveaway.winners, winner]
      };
      this.db.setActiveGiveaway(updatedGiveaway);
    }
  }

  public resetGiveaway(): void {
    const activeGiveaway = this.db.getActiveGiveaway();
    if (activeGiveaway) {
      const updatedGiveaway = {
        ...activeGiveaway,
        entries: [],
        winners: []
      };
      this.db.setActiveGiveaway(updatedGiveaway);
    }
  }

  // Method to handle giveaway entries from chat
  public handleGiveawayEntry(username: string, message: string): boolean {
    const activeGiveaway = this.db.getActiveGiveaway();
    if (!activeGiveaway) return false;

    // Check if message contains the keyword
    if (message.toLowerCase().includes(activeGiveaway.keyword.toLowerCase())) {
      // Check if user already entered
      const alreadyEntered = activeGiveaway.entries.some(entry => entry.username === username);
      if (!alreadyEntered) {
        this.addEntry({
          username,
          timestamp: new Date().toLocaleTimeString()
        });
        return true;
      }
    }
    return false;
  }
}