import { nanoid } from 'nanoid';
import { Database } from './Database';

type GiveawayEntry = {
  username: string;
  timestamp: string;
};

type GiveawayWinner = {
  username: string;
  timestamp: string;
};

type ActiveGiveaway = {
  id: string;
  title: string;
  prize: string;
  keyword: string;
  eligibility: string;
  startTime: string;
  endTime: string;
  timeRemaining: string;
  isPaused: boolean;
  entries: GiveawayEntry[];
  winners: GiveawayWinner[];
  announceStart?: boolean;
  startMessage?: string;
};

type PastGiveaway = {
  id: string;
  title: string;
  prize: string;
  date: string;
  entriesCount: number;
  winner: string;
};



export class GiveawayManager {
  private db: Database;
  private timerInterval: NodeJS.Timeout | null = null;
  private chatCallback: ((message: string) => void) | null = null;

  constructor() {
    this.db = Database.getInstance();
  }

  public setChatCallback(callback: (message: string) => void) {
    this.chatCallback = callback;
  }

  public getActiveGiveaway(): ActiveGiveaway | null {
    return this.db.getActiveGiveaway();
  }

  public getPastGiveaways(): PastGiveaway[] {
    return this.db.getPastGiveaways();
  }

  public startGiveaway(giveaway: Partial<ActiveGiveaway>): void {
    const activeGiveaway = this.db.getActiveGiveaway();
    if (activeGiveaway) {
      this.endGiveaway();
    }

    const durationMinutes = 5; // Default to 5 minutes
    const endTime = new Date(Date.now() + durationMinutes * 60 * 1000).toISOString();

    const newGiveaway = {
      id: nanoid(),
      title: giveaway.title || 'Giveaway',
      prize: giveaway.prize || 'Prize',
      keyword: giveaway.keyword || '!join',
      eligibility: giveaway.eligibility || 'everyone',
      startTime: new Date().toISOString(),
      endTime,
      timeRemaining: `${durationMinutes}:00`,
      isPaused: false,
      entries: [],
      winners: [],
      announceStart: giveaway.announceStart || false,
      startMessage: giveaway.startMessage || ''
    };

    this.db.setActiveGiveaway(newGiveaway);

    // Send announcement message if enabled
    if (newGiveaway.announceStart && newGiveaway.startMessage && this.chatCallback) {
      const message = newGiveaway.startMessage
        .replace('{title}', newGiveaway.title)
        .replace('{prize}', newGiveaway.prize)
        .replace('{keyword}', newGiveaway.keyword);
      this.chatCallback(message);
    }

    // Start countdown timer
    this.startTimer(durationMinutes * 60);

    // Add some test entries for demo purposes
    setTimeout(() => {
      this.addEntry({ username: 'TestUser1', timestamp: new Date().toLocaleTimeString() });
      this.addEntry({ username: 'TestUser2', timestamp: new Date().toLocaleTimeString() });
      this.addEntry({ username: 'TestUser3', timestamp: new Date().toLocaleTimeString() });
      console.log('✅ Added test entries to giveaway');
    }, 2000);
  }

  private startTimer(seconds: number): void {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
    }

    let remainingSeconds = seconds;

    this.timerInterval = setInterval(() => {
      const activeGiveaway = this.db.getActiveGiveaway();
      if (!activeGiveaway || activeGiveaway.isPaused) return;

      remainingSeconds--;

      if (remainingSeconds <= 0) {
        clearInterval(this.timerInterval!);
        this.timerInterval = null;

        // Auto-end the giveaway when timer expires
        this.endGiveaway();
        return;
      }

      const minutes = Math.floor(remainingSeconds / 60);
      const secs = remainingSeconds % 60;

      // Update the giveaway in database
      const updatedGiveaway = {
        ...activeGiveaway,
        timeRemaining: `${minutes}:${secs.toString().padStart(2, '0')}`
      };
      this.db.setActiveGiveaway(updatedGiveaway);
    }, 1000);
  }

  public endGiveaway(): void {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
      this.timerInterval = null;
    }

    this.db.endActiveGiveaway();
  }

  public addEntry(entry: GiveawayEntry): void {
    const activeGiveaway = this.db.getActiveGiveaway();
    if (activeGiveaway) {
      const updatedGiveaway = {
        ...activeGiveaway,
        entries: [...activeGiveaway.entries, entry]
      };
      this.db.setActiveGiveaway(updatedGiveaway);
    }
  }

  public addWinner(winner: GiveawayWinner): void {
    const activeGiveaway = this.db.getActiveGiveaway();
    if (activeGiveaway) {
      const updatedGiveaway = {
        ...activeGiveaway,
        winners: [...activeGiveaway.winners, winner]
      };
      this.db.setActiveGiveaway(updatedGiveaway);
    }
  }

  public resetGiveaway(): void {
    const activeGiveaway = this.db.getActiveGiveaway();
    if (activeGiveaway) {
      const updatedGiveaway = {
        ...activeGiveaway,
        entries: [],
        winners: []
      };
      this.db.setActiveGiveaway(updatedGiveaway);
    }
  }

  // Method to handle giveaway entries from chat
  public handleGiveawayEntry(username: string, message: string): boolean {
    const activeGiveaway = this.db.getActiveGiveaway();
    if (!activeGiveaway) return false;

    // Check if message contains the keyword
    if (message.toLowerCase().includes(activeGiveaway.keyword.toLowerCase())) {
      // Check if user already entered
      const alreadyEntered = activeGiveaway.entries.some(entry => entry.username === username);
      if (!alreadyEntered) {
        this.addEntry({
          username,
          timestamp: new Date().toLocaleTimeString()
        });
        return true;
      }
    }
    return false;
  }

  // Method to draw a winner
  public drawWinner(): GiveawayWinner | null {
    console.log('🎲 Drawing winner...');
    const activeGiveaway = this.db.getActiveGiveaway();

    if (!activeGiveaway) {
      console.log('❌ No active giveaway found');
      return null;
    }

    if (activeGiveaway.entries.length === 0) {
      console.log('❌ No entries found in giveaway');
      return null;
    }

    console.log(`🎯 Found ${activeGiveaway.entries.length} entries`);

    // Randomly select a winner from entries
    const randomIndex = Math.floor(Math.random() * activeGiveaway.entries.length);
    const winnerEntry = activeGiveaway.entries[randomIndex];

    console.log(`🏆 Selected winner: ${winnerEntry.username} (index ${randomIndex})`);

    const winner: GiveawayWinner = {
      username: winnerEntry.username,
      timestamp: new Date().toLocaleTimeString()
    };

    // Add winner to giveaway
    const updatedGiveaway = {
      ...activeGiveaway,
      winners: [...activeGiveaway.winners, winner]
    };
    this.db.setActiveGiveaway(updatedGiveaway);

    console.log(`✅ Winner added to giveaway. Total winners: ${updatedGiveaway.winners.length}`);

    // Send congratulations message
    if (this.chatCallback) {
      const message = `🎉 Congratulations ${winner.username}! You just won ${activeGiveaway.prize}! 🎉`;
      console.log(`📢 Sending congratulations message: ${message}`);
      this.chatCallback(message);
    } else {
      console.log('❌ No chat callback available');
    }

    return winner;
  }

  // Method to get user profile data for modal
  public getUserProfile(username: string): { username: string; messages: any[]; stats: any } {
    const chatMessages = this.db.getChatMessages();
    const userMessages = chatMessages.filter(msg => msg.username.toLowerCase() === username.toLowerCase());

    const stats = {
      totalMessages: userMessages.length,
      firstSeen: userMessages.length > 0 ? new Date(userMessages[0].timestamp).toLocaleDateString() : 'Never',
      lastSeen: userMessages.length > 0 ? new Date(userMessages[userMessages.length - 1].timestamp).toLocaleDateString() : 'Never',
      isMod: userMessages.length > 0 ? userMessages[userMessages.length - 1].isMod : false,
      isSubscriber: userMessages.length > 0 ? userMessages[userMessages.length - 1].isSubscriber : false
    };

    return {
      username,
      messages: userMessages.slice(-50), // Last 50 messages
      stats
    };
  }
}