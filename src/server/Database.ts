import fs from 'fs';
import path from 'path';

export interface DatabaseData {
  commands: any[];
  giveaways: {
    active: any | null;
    past: any[];
  };
  songs: {
    queue: any[];
    current: any | null;
    history: any[];
  };
  chat: {
    messages: any[];
  };
  metrics: {
    totalChatMessages: number;
    totalCommands: number;
    totalSongRequests: number;
    startTime: number;
  };
  settings: {
    [key: string]: any;
  };
}

export class Database {
  private static instance: Database | null = null;
  private dbPath: string;
  private data: DatabaseData;

  private constructor() {
    this.dbPath = path.join(process.cwd(), 'data', 'bot-data.json');
    this.ensureDataDirectory();
    this.loadData();
  }

  public static getInstance(): Database {
    if (!Database.instance) {
      Database.instance = new Database();
    }
    return Database.instance;
  }

  private ensureDataDirectory() {
    const dataDir = path.dirname(this.dbPath);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
  }

  private getDefaultData(): DatabaseData {
    return {
      commands: [
        {
          id: 'default-discord',
          name: 'discord',
          response: 'Join our Discord server at: https://discord.gg/example',
          userLevel: 'everyone',
          cooldown: 10,
          enabled: true,
          usageCount: 0
        },
        {
          id: 'default-uptime',
          name: 'uptime',
          response: 'Stream has been live for {uptime}',
          userLevel: 'everyone',
          cooldown: 5,
          enabled: true,
          usageCount: 0
        }
      ],
      giveaways: {
        active: null,
        past: []
      },
      songs: {
        queue: [],
        current: null,
        history: []
      },
      chat: {
        messages: []
      },
      metrics: {
        totalChatMessages: 0,
        totalCommands: 0,
        totalSongRequests: 0,
        startTime: Date.now()
      },
      settings: {}
    };
  }

  private loadData() {
    try {
      if (fs.existsSync(this.dbPath)) {
        const fileContent = fs.readFileSync(this.dbPath, 'utf8');
        this.data = { ...this.getDefaultData(), ...JSON.parse(fileContent) };
      } else {
        this.data = this.getDefaultData();
        this.saveData();
      }
    } catch (error) {
      console.error('Error loading database:', error);
      this.data = this.getDefaultData();
    }
  }

  private saveData() {
    try {
      fs.writeFileSync(this.dbPath, JSON.stringify(this.data, null, 2));
    } catch (error) {
      console.error('Error saving database:', error);
    }
  }

  // Commands
  public getCommands() {
    return this.data.commands;
  }

  public addCommand(command: any) {
    this.data.commands.push(command);
    this.saveData();
  }

  public updateCommand(command: any) {
    const index = this.data.commands.findIndex(c => c.id === command.id);
    if (index !== -1) {
      this.data.commands[index] = command;
      this.saveData();
    }
  }

  public deleteCommand(id: string) {
    this.data.commands = this.data.commands.filter(c => c.id !== id);
    this.saveData();
  }

  public incrementCommandUsage(commandName: string) {
    const command = this.data.commands.find(c => c.name === commandName);
    if (command) {
      command.usageCount++;
      this.data.metrics.totalCommands++;
      this.saveData();
    }
  }

  // Giveaways
  public getActiveGiveaway() {
    return this.data.giveaways.active;
  }

  public setActiveGiveaway(giveaway: any) {
    this.data.giveaways.active = giveaway;
    this.saveData();
  }

  public endActiveGiveaway() {
    if (this.data.giveaways.active) {
      this.data.giveaways.past.unshift(this.data.giveaways.active);
      this.data.giveaways.active = null;
      this.saveData();
    }
  }

  public getPastGiveaways() {
    return this.data.giveaways.past;
  }

  // Songs
  public getSongQueue() {
    return this.data.songs.queue;
  }

  public setSongQueue(queue: any[]) {
    this.data.songs.queue = queue;
    this.saveData();
  }

  public addSongToQueue(song: any) {
    this.data.songs.queue.push(song);
    this.data.metrics.totalSongRequests++;
    this.saveData();
  }

  public removeSongFromQueue(id: string) {
    this.data.songs.queue = this.data.songs.queue.filter(s => s.id !== id);
    this.saveData();
  }

  public getCurrentSong() {
    return this.data.songs.current;
  }

  public setCurrentSong(song: any) {
    if (this.data.songs.current) {
      this.data.songs.history.unshift(this.data.songs.current);
    }
    this.data.songs.current = song;
    this.saveData();
  }

  // Chat
  public addChatMessage(message: any) {
    this.data.chat.messages.push(message);
    this.data.metrics.totalChatMessages++;
    
    // Keep only last 1000 messages
    if (this.data.chat.messages.length > 1000) {
      this.data.chat.messages = this.data.chat.messages.slice(-1000);
    }
    
    this.saveData();
  }

  public getChatMessages() {
    return this.data.chat.messages.slice(-100); // Return last 100 messages
  }

  // Metrics
  public getMetrics() {
    const uptime = Date.now() - this.data.metrics.startTime;
    const hours = Math.floor(uptime / (1000 * 60 * 60));
    const minutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((uptime % (1000 * 60)) / 1000);

    return {
      chatMessages: this.data.metrics.totalChatMessages,
      viewers: Math.floor(Math.random() * 50) + 10, // Simulated viewer count
      commandsUsed: this.data.metrics.totalCommands,
      uptime: `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`,
      songRequests: this.data.metrics.totalSongRequests
    };
  }

  // Settings
  public getSetting(key: string, defaultValue: any = null) {
    return this.data.settings[key] || defaultValue;
  }

  public setSetting(key: string, value: any) {
    this.data.settings[key] = value;
    this.saveData();
  }
}
