# Twitch Bot Setup Guide

This guide will help you connect your Twitch bot to your Twitch channel using a user account token.

## Prerequisites

1. A Twitch account
2. Node.js installed on your system
3. This project set up and running

## Step 1: Get Your Twitch OAuth Token

### Option A: Using Twitch Token Generator (Recommended for testing)

1. Go to [https://twitchtokengenerator.com/](https://twitchtokengenerator.com/)
2. Click "Generate Token"
3. Log in with your Twitch account
4. Select the scopes you need:
   - `chat:read` - Read chat messages
   - `chat:edit` - Send chat messages
   - `channel:moderate` - Moderate chat (if you want mod features)
5. Copy the OAuth token (it will start with `oauth:`)

### Option B: Manual OAuth Flow (For production)

1. Create a Twitch application at [https://dev.twitch.tv/console/apps](https://dev.twitch.tv/console/apps)
2. Set up OAuth redirect URL
3. Use the OAuth flow to get a token with required scopes

## Step 2: Set Up Environment Variables

1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file with your credentials:
   ```env
   # Twitch Bot Configuration
   TWITCH_USERNAME=your_bot_username
   TWITCH_OAUTH_TOKEN=oauth:your_oauth_token_here
   TWITCH_CHANNEL=your_channel_name

   # Optional: YouTube API for song search
   YOUTUBE_API_KEY=your_youtube_api_key_here

   # Server Configuration
   PORT=3001
   NODE_ENV=development
   ```

### Required Variables:
- `TWITCH_USERNAME`: Your bot's username (can be your main account)
- `TWITCH_OAUTH_TOKEN`: The OAuth token from Step 1 (must include `oauth:` prefix)
- `TWITCH_CHANNEL`: The channel name to connect to (without #)

### Optional Variables:
- `YOUTUBE_API_KEY`: For enhanced song search functionality

## Step 3: Get YouTube API Key (Optional)

If you want proper YouTube song search instead of mock data:

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the YouTube Data API v3
4. Create credentials (API Key)
5. Add the API key to your `.env` file

## Step 4: Start the Bot

1. Install dependencies (if not already done):
   ```bash
   npm install
   ```

2. Start the development server:
   ```bash
   npm run dev:all
   ```

3. The bot should connect to Twitch chat automatically

## Step 5: Test the Bot

1. Open your Twitch channel in a browser
2. Type in chat: `!sr Never Gonna Give You Up`
3. The bot should respond and add the song to the queue
4. Check the song requests page in the web interface

## Available Chat Commands

- `!sr <song>` or `!songrequest <song>` - Request a song
- `!uptime` - Show stream uptime
- `!queue` - Show queue info

## Troubleshooting

### Bot not connecting:
- Check that your OAuth token is valid and includes `oauth:` prefix
- Verify your username and channel name are correct
- Check the console for error messages

### Song requests not working:
- Ensure the bot is connected to chat
- Check that the YouTube API key is valid (if using)
- Look for error messages in the server console

### Permission issues:
- Make sure your OAuth token has the required scopes
- If using a separate bot account, make sure it's not banned from the channel

## Security Notes

- Never share your OAuth token publicly
- Add `.env` to your `.gitignore` file
- Regenerate tokens periodically for security
- Use environment variables in production, not hardcoded values

## Next Steps

Once connected, you can:
- Customize chat commands in `src/server/TwitchBot.ts`
- Add more song sources beyond YouTube
- Implement user permissions and cooldowns
- Add more interactive features

For more advanced features, consider implementing:
- Twitch EventSub for real-time events
- Database storage for persistent data
- User point systems
- Custom overlays
