<!DOCTYPE html>
<html>
<head>
    <title>Socket.IO Test</title>
</head>
<body>
    <h1>Socket.IO Connection Test</h1>
    <div id="status">Connecting...</div>
    <div id="logs"></div>

    <script src="https://cdn.socket.io/4.7.4/socket.io.min.js"></script>
    <script>
        const statusDiv = document.getElementById('status');
        const logsDiv = document.getElementById('logs');
        
        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            logsDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
        }
        
        addLog('Attempting to connect to http://localhost:3001');
        
        const socket = io('http://localhost:3001', {
            transports: ['polling', 'websocket']
        });
        
        socket.on('connect', () => {
            statusDiv.innerHTML = '✅ Connected!';
            statusDiv.style.color = 'green';
            addLog('✅ Connected successfully!');
            
            // Test a simple emit
            socket.emit('getMetrics');
        });
        
        socket.on('disconnect', (reason) => {
            statusDiv.innerHTML = '❌ Disconnected: ' + reason;
            statusDiv.style.color = 'red';
            addLog('❌ Disconnected: ' + reason);
        });
        
        socket.on('connect_error', (error) => {
            statusDiv.innerHTML = '🔥 Connection Error: ' + error.message;
            statusDiv.style.color = 'red';
            addLog('🔥 Connection Error: ' + error.message);
        });
        
        socket.on('metrics', (data) => {
            addLog('✅ Received metrics: ' + JSON.stringify(data));
        });
    </script>
</body>
</html>
